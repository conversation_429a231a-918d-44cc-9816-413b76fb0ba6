import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StatusBar,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import ApiService from "../services/api";

const ServicesScreen = () => {
  const [services, setServices] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingService, setEditingService] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    durationMins: 30,
    price: "",
  });
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    loadServices();
  }, []);

  const loadServices = async (forceRefresh = false) => {
    try {
      setIsLoading(true);

      // If forceRefresh is true, always fetch from API
      if (!forceRefresh) {
        // First try to get services from stored user data (from login)
        const storedUserData = await AsyncStorage.getItem("userData");
        if (storedUserData) {
          const userData = JSON.parse(storedUserData);
          if (userData.services && Array.isArray(userData.services)) {
            setServices(userData.services);
            setIsLoading(false);
            setIsRefreshing(false);
            return;
          }
        }
      }

      // Fetch from API (either as fallback or forced refresh)
      const data = await ApiService.getServices();
      setServices(data.services || []);

      // Update stored user data with fresh services
      const storedUserData = await AsyncStorage.getItem("userData");
      if (storedUserData) {
        const userData = JSON.parse(storedUserData);
        userData.services = data.services || [];
        await AsyncStorage.setItem("userData", JSON.stringify(userData));
      }
    } catch (error) {
      console.error("Error loading services:", error);
      Alert.alert("Greška", "Nije moguće učitati usluge");
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const onRefresh = () => {
    setIsRefreshing(true);
    loadServices(true); // Force refresh from API
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      durationMins: 30,
      price: "",
    });
    setEditingService(null);
  };

  const handleAddService = () => {
    if (showForm) {
      setShowForm(false);
      resetForm();
    } else {
      resetForm();
      setShowForm(true);
    }
  };

  const handleEditService = (service) => {
    setEditingService(service);
    setFormData({
      name: service.name || "",
      description: service.description || "",
      durationMins: service.durationMins || 30,
      price: service.price || "",
    });
    setShowForm(true);
  };

  const handleFormSubmit = async () => {
    if (!formData.name.trim()) {
      Alert.alert("Greška", "Naziv usluge je obavezan");
      return;
    }

    if (!formData.durationMins || formData.durationMins < 5) {
      Alert.alert("Greška", "Trajanje mora biti najmanje 5 minuta");
      return;
    }

    setIsSaving(true);

    try {
      if (editingService) {
        // Update existing service
        await ApiService.updateService({
          serviceId: editingService._id,
          ...formData,
        });
        Alert.alert("Uspeh", "Usluga je uspešno ažurirana");
      } else {
        // Create new service
        await ApiService.createService(formData);
        Alert.alert("Uspeh", "Usluga je uspešno dodana");
      }

      setShowForm(false);
      resetForm();
      await loadServices(true); // Force reload services from API
    } catch (error) {
      console.error("Error saving service:", error);
      Alert.alert("Greška", "Nije moguće sačuvati uslugu");
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: field === "durationMins" ? parseInt(value, 10) || 0 : value,
    }));
  };

  const handleDeleteService = (service) => {
    Alert.alert(
      "Brisanje usluge",
      `Da li ste sigurni da želite da obrišete uslugu "${service.name}"?`,
      [
        { text: "Otkaži", style: "cancel" },
        {
          text: "Obriši",
          style: "destructive",
          onPress: () => deleteService(service._id),
        },
      ]
    );
  };

  const deleteService = async (serviceId) => {
    try {
      setIsLoading(true);
      await ApiService.deleteService(serviceId);
      Alert.alert("Uspeh", "Usluga je uspešno obrisana");
      await loadServices(true); // Force reload services from API
    } catch (error) {
      console.error("Error deleting service:", error);
      Alert.alert("Greška", "Nije moguće obrisati uslugu");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2563eb" />
        <Text style={styles.loadingText}>Učitavanje usluga...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Usluge</Text>
          <TouchableOpacity style={styles.addButton} onPress={handleAddService}>
            <Text style={styles.addButtonText}>
              {showForm ? "Otkaži" : "+ Dodaj"}
            </Text>
          </TouchableOpacity>
        </View>

        {showForm && (
          <View style={styles.formContainer}>
            <Text style={styles.formTitle}>
              {editingService ? "Uredi uslugu" : "Nova usluga"}
            </Text>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>Naziv usluge</Text>
              <TextInput
                style={styles.input}
                value={formData.name}
                onChangeText={(value) => handleInputChange("name", value)}
                placeholder="npr. Muško šišanje, Manikir..."
                placeholderTextColor="#9ca3af"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>Opis usluge</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={formData.description}
                onChangeText={(value) =>
                  handleInputChange("description", value)
                }
                placeholder="Kratki opis usluge..."
                placeholderTextColor="#9ca3af"
                multiline
                numberOfLines={3}
              />
            </View>

            <View style={styles.inputRow}>
              <View
                style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}
              >
                <Text style={styles.label}>Trajanje (min)</Text>
                <TextInput
                  style={styles.input}
                  value={formData.durationMins.toString()}
                  onChangeText={(value) =>
                    handleInputChange("durationMins", value)
                  }
                  placeholder="30"
                  placeholderTextColor="#9ca3af"
                  keyboardType="numeric"
                />
              </View>

              <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
                <Text style={styles.label}>Cena (RSD)</Text>
                <TextInput
                  style={styles.input}
                  value={formData.price}
                  onChangeText={(value) => handleInputChange("price", value)}
                  placeholder="1200"
                  placeholderTextColor="#9ca3af"
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.formActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => {
                  setShowForm(false);
                  resetForm();
                }}
              >
                <Text style={styles.cancelButtonText}>Otkaži</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.saveButton, isSaving && styles.disabledButton]}
                onPress={handleFormSubmit}
                disabled={isSaving}
              >
                {isSaving ? (
                  <ActivityIndicator color="#ffffff" size="small" />
                ) : (
                  <Text style={styles.saveButtonText}>
                    {editingService ? "Sačuvaj izmene" : "Dodaj uslugu"}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        )}

        <ScrollView
          style={styles.servicesList}
          refreshControl={
            <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
          }
        >
          {services.length > 0 ? (
            services.map((service, index) => (
              <View key={service._id || index} style={styles.serviceCard}>
                <View style={styles.serviceHeader}>
                  <Text style={styles.serviceName}>{service.name}</Text>
                  <View style={styles.serviceActions}>
                    <TouchableOpacity
                      style={styles.editButton}
                      onPress={() => handleEditService(service)}
                    >
                      <Text style={styles.editButtonText}>✏️</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.deleteButton}
                      onPress={() => handleDeleteService(service)}
                    >
                      <Text style={styles.deleteButtonText}>🗑️</Text>
                    </TouchableOpacity>
                  </View>
                </View>

                {service.description && (
                  <Text style={styles.serviceDescription}>
                    {service.description}
                  </Text>
                )}

                <View style={styles.serviceDetails}>
                  <View style={styles.serviceDetail}>
                    <Text style={styles.detailLabel}>Trajanje:</Text>
                    <Text style={styles.detailValue}>
                      {service.durationMins} min
                    </Text>
                  </View>
                  {service.price && (
                    <View style={styles.serviceDetail}>
                      <Text style={styles.detailLabel}>Cena:</Text>
                      <Text style={styles.detailValue}>{service.price}</Text>
                    </View>
                  )}
                </View>
              </View>
            ))
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateTitle}>Nema usluga</Text>
              <Text style={styles.emptyStateText}>
                Dodajte vašu prvi uslugu da biste počeli
              </Text>
              <TouchableOpacity
                style={styles.emptyStateButton}
                onPress={handleAddService}
              >
                <Text style={styles.emptyStateButtonText}>Dodaj uslugu</Text>
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
    paddingTop: Platform.OS === "android" ? StatusBar.currentHeight : 0,
  },
  keyboardContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f8f9fa",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#6b7280",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    backgroundColor: "#ffffff",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1f2937",
  },
  addButton: {
    backgroundColor: "#2563eb",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  addButtonText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  servicesList: {
    flex: 1,
    padding: 20,
  },
  serviceCard: {
    backgroundColor: "#ffffff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  serviceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  serviceName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1f2937",
    flex: 1,
  },
  serviceActions: {
    flexDirection: "row",
    gap: 8,
  },
  editButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: "#f3f4f6",
  },
  editButtonText: {
    fontSize: 16,
  },
  deleteButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: "#fef2f2",
  },
  deleteButtonText: {
    fontSize: 16,
  },
  serviceDescription: {
    fontSize: 14,
    color: "#6b7280",
    marginBottom: 12,
  },
  serviceDetails: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  serviceDetail: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailLabel: {
    fontSize: 14,
    color: "#6b7280",
    marginRight: 4,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: "600",
    color: "#1f2937",
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
    backgroundColor: "#ffffff",
    borderRadius: 12,
    marginTop: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: "#6b7280",
    textAlign: "center",
    marginBottom: 24,
  },
  emptyStateButton: {
    backgroundColor: "#2563eb",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  formContainer: {
    backgroundColor: "#ffffff",
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  formTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputRow: {
    flexDirection: "row",
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    color: "#374151",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: "#d1d5db",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: "#ffffff",
    color: "#1f2937",
  },
  textArea: {
    height: 80,
    textAlignVertical: "top",
  },
  formActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 12,
    marginTop: 8,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#f3f4f6",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  cancelButtonText: {
    color: "#374151",
    fontWeight: "600",
  },
  saveButton: {
    flex: 1,
    backgroundColor: "#2563eb",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  saveButtonText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  disabledButton: {
    backgroundColor: "#9ca3af",
  },
});

export default ServicesScreen;
