{"scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "dev": "expo start", "build:android": "eas build --platform android --profile", "build:android:preview": "eas build --platform android --profile preview"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.2", "@react-navigation/bottom-tabs": "^7.4.1", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.1", "expo": "~53.0.0", "expo-build-properties": "~0.13.0", "expo-dev-client": "~5.2.1", "expo-modules-core": "~2.4.0", "expo-status-bar": "~2.0.0", "lodash": "^4.17.21", "react": "19.0.0", "react-native": "0.79.4", "react-native-call-detection": "^1.9.0", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "^2.26.0", "react-native-safe-area-context": "^5.5.0", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@expo/config-plugins": "~10.0.3", "@react-native-community/cli": "^18.0.0", "@types/react": "^19.1.8", "eas-cli": "latest"}}