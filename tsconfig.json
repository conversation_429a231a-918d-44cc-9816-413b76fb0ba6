{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-native", "lib": ["es2017"], "moduleResolution": "node", "noEmit": true, "strict": false, "target": "esnext", "typeRoots": ["./types", "./node_modules/@types"], "types": ["react-native"]}, "include": ["src/**/*", "types/**/*"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"], "extends": "expo/tsconfig.base"}