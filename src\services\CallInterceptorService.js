import {
  NativeModules,
  Platform,
  Alert,
  PermissionsAndroid,
  DeviceEventEmitter,
  NativeEventEmitter,
  Linking,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Kompatibilna implementacija za Hermes engine
class CallInterceptorService {
  constructor() {
    this.isServiceRunning = false;
    this.currentSettings = null;
    this.callStateListener = null;
    this.phoneStateListener = null;
  }

  async startService() {
    try {
      if (Platform.OS !== "android") {
        throw new Error("Call detection is only supported on Android");
      }

      console.log("🚀 Starting call detection service...");

      // Load current settings
      this.currentSettings = await this.getSettings();
      console.log("⚙️ Loaded settings:", this.currentSettings);

      // Pokušaj da koristiš kompatibilnu detekciju poziva
      await this.setupCallDetection();

      this.isServiceRunning = true;
      console.log("✅ Call detection service started");

      return { success: true, message: "Call detection started" };
    } catch (error) {
      console.error("❌ Error starting service:", error);
      throw error;
    }
  }

  async setupCallDetection() {
    try {
      // Koristimo native Android TelecomManager kroz bridge
      const { CallDetectionModule } = NativeModules;

      if (CallDetectionModule) {
        // Ako imamo custom native modul
        await CallDetectionModule.startCallDetection();

        // Slušaj eventi
        const eventEmitter = new NativeEventEmitter(CallDetectionModule);
        this.callStateListener = eventEmitter.addListener(
          "CallStateChanged",
          this.handleCallEvent.bind(this)
        );
        console.log("✅ Using native call detection module");
      } else {
        // Fallback: koristi PhoneStateListener preko bridge-a
        console.log("📱 Using fallback call detection method");
        await this.setupFallbackDetection();
      }
    } catch (error) {
      console.error("❌ Error setting up call detection:", error);
      // Nastavi bez detekcije poziva ali omogući manuelno slanje SMS-a
      console.log("⚠️ Call detection disabled, manual SMS only");
    }
  }

  async setupFallbackDetection() {
    try {
      // Koristi DeviceEventEmitter za sistemske eventi
      this.phoneStateListener = DeviceEventEmitter.addListener(
        "PhoneStateChanged",
        this.handlePhoneStateChange.bind(this)
      );

      // Registruj listener za sistemske pozive
      if (NativeModules.PhoneStateModule) {
        await NativeModules.PhoneStateModule.startListening();
        console.log("✅ Phone state listener started");
      } else {
        console.log("⚠️ No phone state module available");
      }
    } catch (error) {
      console.log("⚠️ Fallback detection setup failed:", error);
    }
  }

  async stopService() {
    try {
      console.log("🛑 Stopping call detection service...");

      // Ukloni listenere
      if (this.callStateListener) {
        this.callStateListener.remove();
        this.callStateListener = null;
      }

      if (this.phoneStateListener) {
        this.phoneStateListener.remove();
        this.phoneStateListener = null;
      }

      // Zaustavi native module
      if (NativeModules.CallDetectionModule) {
        await NativeModules.CallDetectionModule.stopCallDetection();
      }

      if (NativeModules.PhoneStateModule) {
        await NativeModules.PhoneStateModule.stopListening();
      }

      this.isServiceRunning = false;
      console.log("✅ Call detection service stopped");

      return { success: true, message: "Call detection stopped" };
    } catch (error) {
      console.error("❌ Error stopping service:", error);
      throw error;
    }
  }

  handleCallEvent(event) {
    try {
      console.log("📞 CALL EVENT:", event);

      const { state, phoneNumber } = event;

      switch (state) {
        case "RINGING":
        case "INCOMING":
          console.log("📲 INCOMING CALL from:", phoneNumber);
          this.handleIncomingCall(phoneNumber);
          break;
        case "OFFHOOK":
          console.log("📞 CALL ANSWERED");
          break;
        case "IDLE":
          console.log("📴 CALL ENDED");
          break;
        case "MISSED":
          console.log("📵 MISSED CALL from:", phoneNumber);
          this.handleMissedCall(phoneNumber);
          break;
        default:
          console.log("❓ Unknown call state:", state);
      }
    } catch (error) {
      console.error("❌ Error handling call event:", error);
    }
  }

  handlePhoneStateChange(event) {
    try {
      console.log("📱 PHONE STATE CHANGED:", event);

      // Konvertuj phone state u call event format
      const callEvent = {
        state: event.state,
        phoneNumber: event.phoneNumber || event.incomingNumber,
      };

      this.handleCallEvent(callEvent);
    } catch (error) {
      console.error("❌ Error handling phone state change:", error);
    }
  }

  async handleIncomingCall(phoneNumber) {
    try {
      if (!this.currentSettings || !this.currentSettings.serviceEnabled) {
        console.log("⏭️ Service disabled, ignoring call");
        return;
      }

      const shouldHandle = this.shouldHandleCall(this.currentSettings);
      console.log("🤔 Should handle call:", shouldHandle, "from:", phoneNumber);

      if (shouldHandle) {
        console.log("🚫 HANDLING INCOMING CALL - will send SMS");
        // SMS će biti poslat kada poziv postane propušten
      } else {
        console.log("✅ ALLOWING CALL to ring normally");
      }
    } catch (error) {
      console.error("❌ Error handling incoming call:", error);
    }
  }

  async handleMissedCall(phoneNumber) {
    try {
      if (!this.currentSettings || !this.currentSettings.serviceEnabled) {
        console.log("⏭️ Service disabled, ignoring missed call");
        return;
      }

      if (this.currentSettings.smsEnabled && this.currentSettings.smsTemplate) {
        console.log("📱 Sending SMS for missed call to:", phoneNumber);
        await this.sendSMS(phoneNumber, this.currentSettings.smsTemplate);
      }
    } catch (error) {
      console.error("❌ Error handling missed call:", error);
    }
  }

  shouldHandleCall(settings) {
    if (!settings.serviceEnabled) {
      return false;
    }

    switch (settings.callHandlingMode) {
      case "always_allow":
        return false;
      case "always_reject":
        return true;
      case "business_hours_only":
        return !this.isInWorkingHours();
      default:
        return false;
    }
  }

  isInWorkingHours() {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Simple check: 9 AM to 5 PM, Monday to Friday
    const isWeekday = day >= 1 && day <= 5;
    const isWorkingHour = hour >= 9 && hour < 17;

    return isWeekday && isWorkingHour;
  }

  async sendSMS(phoneNumber, message) {
    try {
      if (Platform.OS !== "android") {
        throw new Error("SMS sending is only supported on Android");
      }

      console.log("📱 Sending SMS to:", phoneNumber);
      console.log("💬 Message:", message);

      // Koristi native SMS module umesto problematične biblioteke
      if (NativeModules.SMSModule) {
        await NativeModules.SMSModule.sendSMS(phoneNumber, message);
        console.log("✅ SMS sent via native module");
      } else {
        // Fallback: koristi Linking API za otvaranje SMS aplikacije
        const url = `sms:${phoneNumber}?body=${encodeURIComponent(message)}`;
        await Linking.openURL(url);
        console.log("📱 Opened SMS app with pre-filled message");
      }

      return { success: true, message: "SMS sent" };
    } catch (error) {
      console.error("❌ Error sending SMS:", error);
      throw error;
    }
  }

  async sendManualSMS(phoneNumber, customMessage = null) {
    try {
      console.log("📱 Manual SMS request to:", phoneNumber);

      // Load current settings
      this.currentSettings = await this.getSettings();

      if (!this.currentSettings || !this.currentSettings.serviceEnabled) {
        throw new Error("Service is disabled");
      }

      const message = customMessage || this.currentSettings.smsTemplate;
      if (!message) {
        throw new Error("No SMS template configured");
      }

      await this.sendSMS(phoneNumber, message);
      return { success: true, message: "SMS sent successfully" };
    } catch (error) {
      console.error("❌ Error sending manual SMS:", error);
      throw error;
    }
  }

  cleanup() {
    if (this.callStateListener) {
      this.callStateListener.remove();
      this.callStateListener = null;
    }
    if (this.phoneStateListener) {
      this.phoneStateListener.remove();
      this.phoneStateListener = null;
    }
    this.isServiceRunning = false;
    console.log("🧹 Service cleaned up");
  }

  async getSettings() {
    try {
      const savedSettings = await AsyncStorage.getItem("mobileSettings");
      if (savedSettings) {
        return JSON.parse(savedSettings);
      }
      return {
        serviceEnabled: false,
        callHandlingMode: "business_hours_only",
        smsEnabled: true,
        smsTemplate:
          "Zdravo! Hvala što ste pozvali {businessName}. Možete zakazati termin ovde: {bookingLink}",
      };
    } catch (error) {
      console.error("Error loading settings:", error);
      return null;
    }
  }

  async saveSettings(settings) {
    try {
      await AsyncStorage.setItem("mobileSettings", JSON.stringify(settings));
      this.currentSettings = settings;
      console.log("⚙️ Settings saved:", settings);
    } catch (error) {
      console.error("Error saving settings:", error);
      throw error;
    }
  }
}

export default new CallInterceptorService();
