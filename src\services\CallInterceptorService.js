import { Alert } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as SMS from "expo-sms";

class CallInterceptorService {
  constructor() {
    this.isServiceRunning = false;
    this.currentSettings = null;
  }

  async startService() {
    try {
      console.log("🚀 Starting SMS service...");

      // Load current settings
      this.currentSettings = await this.getSettings();
      console.log("⚙️ Loaded settings:", this.currentSettings);

      // Check if SMS is available
      const isAvailable = await SMS.isAvailableAsync();
      if (!isAvailable) {
        throw new Error("SMS is not available on this device");
      }

      this.isServiceRunning = true;
      console.log("✅ SMS service started");

      Alert.alert(
        "Servis pokrenut",
        "SMS servis je aktivan. Pošto Expo ne podržava automatsku detekciju poziva, možete koristiti dugme 'Pošalji SMS' za slanje poruka."
      );

      return { success: true, message: "SMS service started" };
    } catch (error) {
      console.error("❌ Error starting service:", error);
      throw error;
    }
  }

  async stopService() {
    try {
      console.log("🛑 Stopping SMS service...");

      this.isServiceRunning = false;
      console.log("✅ SMS service stopped");

      return { success: true, message: "SMS service stopped" };
    } catch (error) {
      console.error("❌ Error stopping service:", error);
      throw error;
    }
  }

  // Expo ne podržava automatsku detekciju poziva
  // Ova funkcija je zamenjena sa manuelnim slanjem SMS-a
  async sendManualSMS(phoneNumber, customMessage = null) {
    try {
      console.log("📱 Manual SMS request to:", phoneNumber);

      // Load current settings
      this.currentSettings = await this.getSettings();

      if (!this.currentSettings || !this.currentSettings.serviceEnabled) {
        throw new Error("Service is disabled");
      }

      const message = customMessage || this.currentSettings.smsTemplate;
      if (!message) {
        throw new Error("No SMS template configured");
      }

      await this.sendSMS(phoneNumber, message);
      return { success: true, message: "SMS sent successfully" };
    } catch (error) {
      console.error("❌ Error sending manual SMS:", error);
      throw error;
    }
  }

  // Utility function to check if it's working hours (can be used for manual SMS logic)
  isInWorkingHours() {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Simple check: 9 AM to 5 PM, Monday to Friday
    const isWeekday = day >= 1 && day <= 5;
    const isWorkingHour = hour >= 9 && hour < 17;

    return isWeekday && isWorkingHour;
  }

  async sendSMS(phoneNumber, message) {
    try {
      console.log("📱 Sending SMS to:", phoneNumber);
      console.log("💬 Message:", message);

      // Check if SMS is available
      const isAvailable = await SMS.isAvailableAsync();
      if (!isAvailable) {
        throw new Error("SMS is not available on this device");
      }

      // Send SMS using Expo SMS
      const result = await SMS.sendSMSAsync([phoneNumber], message);

      console.log("✅ SMS sent successfully:", result);
      return { success: true, message: "SMS sent", result };
    } catch (error) {
      console.error("❌ Error sending SMS:", error);
      throw error;
    }
  }

  async getSettings() {
    try {
      const savedSettings = await AsyncStorage.getItem("mobileSettings");
      if (savedSettings) {
        return JSON.parse(savedSettings);
      }
      return {
        serviceEnabled: false,
        callHandlingMode: "business_hours_only",
        smsEnabled: true,
        smsTemplate:
          "Zdravo! Hvala što ste pozvali {businessName}. Možete zakazati termin ovde: {bookingLink}",
      };
    } catch (error) {
      console.error("Error loading settings:", error);
      return null;
    }
  }

  async saveSettings(settings) {
    try {
      await AsyncStorage.setItem("mobileSettings", JSON.stringify(settings));
      this.currentSettings = settings;
      console.log("⚙️ Settings saved:", settings);
    } catch (error) {
      console.error("Error saving settings:", error);
      throw error;
    }
  }

  async testSMS(phoneNumber, message) {
    try {
      console.log("🧪 Testing SMS functionality...");
      return await this.sendSMS(phoneNumber, message);
    } catch (error) {
      console.error("❌ Error testing SMS:", error);
      throw error;
    }
  }

  getServiceStatus() {
    return this.isServiceRunning;
  }

  cleanup() {
    this.isServiceRunning = false;
    console.log("🧹 Service cleaned up");
  }
}

// Export singleton instance
export default new CallInterceptorService();
