import {
  NativeModules,
  Platform,
  Alert,
  PermissionsAndroid,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import CallDetectorManager from "react-native-call-detection";
import { SendDirectSms } from "react-native-send-direct-sms";

class CallInterceptorService {
  constructor() {
    this.callDetector = null;
    this.isServiceRunning = false;
    this.currentSettings = null;
  }

  async startService() {
    try {
      if (Platform.OS !== "android") {
        throw new Error("Call detection is only supported on Android");
      }

      console.log("🚀 Starting call detection service...");

      // Load current settings
      this.currentSettings = await this.getSettings();
      console.log("⚙️ Loaded settings:", this.currentSettings);

      // Start call detection
      this.callDetector = new CallDetectorManager(
        (event, phoneNumber) => {
          this.handleCallEvent(event, phoneNumber);
        },
        true, // Read phone number
        () => {
          console.log("✅ Call detector started successfully");
        },
        () => {
          console.log("❌ Call detector failed to start");
        }
      );

      this.isServiceRunning = true;
      console.log("✅ Call detection service started");

      return { success: true, message: "Call detection started" };
    } catch (error) {
      console.error("❌ Error starting service:", error);
      throw error;
    }
  }

  async stopService() {
    try {
      console.log("🛑 Stopping call detection service...");

      if (this.callDetector) {
        this.callDetector.dispose();
        this.callDetector = null;
      }

      this.isServiceRunning = false;
      console.log("✅ Call detection service stopped");

      return { success: true, message: "Call detection stopped" };
    } catch (error) {
      console.error("❌ Error stopping service:", error);
      throw error;
    }
  }

  async handleCallEvent(event, phoneNumber) {
    try {
      console.log("📞 CALL EVENT:", event, "from:", phoneNumber);

      // Reload settings for each call
      this.currentSettings = await this.getSettings();

      switch (event) {
        case "Incoming":
          console.log("📲 INCOMING CALL from:", phoneNumber);
          await this.handleIncomingCall(phoneNumber);
          break;
        case "Offhook":
          console.log("📞 CALL ANSWERED");
          break;
        case "Disconnected":
          console.log("📴 CALL ENDED");
          break;
        case "Missed":
          console.log("📵 MISSED CALL from:", phoneNumber);
          await this.handleMissedCall(phoneNumber);
          break;
        default:
          console.log("❓ Unknown call event:", event);
      }
    } catch (error) {
      console.error("❌ Error handling call event:", error);
    }
  }

  async handleIncomingCall(phoneNumber) {
    try {
      if (!this.currentSettings || !this.currentSettings.serviceEnabled) {
        console.log("⏭️ Service disabled, ignoring call");
        return;
      }

      const shouldHandle = this.shouldHandleCall(this.currentSettings);
      console.log("🤔 Should handle call:", shouldHandle);

      if (shouldHandle) {
        console.log("🚫 HANDLING INCOMING CALL - will send SMS");
        // Note: We can't automatically reject calls with these libraries
        // SMS will be sent when call becomes missed or ends
      } else {
        console.log("✅ ALLOWING CALL to ring normally");
      }
    } catch (error) {
      console.error("❌ Error handling incoming call:", error);
    }
  }

  async handleMissedCall(phoneNumber) {
    try {
      if (!this.currentSettings || !this.currentSettings.serviceEnabled) {
        console.log("⏭️ Service disabled, ignoring missed call");
        return;
      }

      if (this.currentSettings.smsEnabled && this.currentSettings.smsTemplate) {
        console.log("📱 Sending SMS for missed call to:", phoneNumber);
        await this.sendSMS(phoneNumber, this.currentSettings.smsTemplate);
      }
    } catch (error) {
      console.error("❌ Error handling missed call:", error);
    }
  }

  shouldHandleCall(settings) {
    if (!settings.serviceEnabled) {
      return false;
    }

    switch (settings.callHandlingMode) {
      case "always_allow":
        return false;
      case "always_reject":
        return true;
      case "business_hours_only":
        return !this.isInWorkingHours();
      default:
        return false;
    }
  }

  async sendManualSMS(phoneNumber, customMessage = null) {
    try {
      console.log("📱 Manual SMS request to:", phoneNumber);

      // Load current settings
      this.currentSettings = await this.getSettings();

      if (!this.currentSettings || !this.currentSettings.serviceEnabled) {
        throw new Error("Service is disabled");
      }

      const message = customMessage || this.currentSettings.smsTemplate;
      if (!message) {
        throw new Error("No SMS template configured");
      }

      await this.sendSMS(phoneNumber, message);
      return { success: true, message: "SMS sent successfully" };
    } catch (error) {
      console.error("❌ Error sending manual SMS:", error);
      throw error;
    }
  }

  // Utility function to check if it's working hours (can be used for manual SMS logic)
  isInWorkingHours() {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Simple check: 9 AM to 5 PM, Monday to Friday
    const isWeekday = day >= 1 && day <= 5;
    const isWorkingHour = hour >= 9 && hour < 17;

    return isWeekday && isWorkingHour;
  }

  async sendSMS(phoneNumber, message) {
    try {
      if (Platform.OS !== "android") {
        throw new Error("SMS sending is only supported on Android");
      }

      console.log("📱 Sending SMS to:", phoneNumber);
      console.log("💬 Message:", message);

      await SendDirectSms(phoneNumber, message);

      console.log("✅ SMS sent successfully");
      return { success: true, message: "SMS sent" };
    } catch (error) {
      console.error("❌ Error sending SMS:", error);
      throw error;
    }
  }

  async getSettings() {
    try {
      const savedSettings = await AsyncStorage.getItem("mobileSettings");
      if (savedSettings) {
        return JSON.parse(savedSettings);
      }
      return {
        serviceEnabled: false,
        callHandlingMode: "business_hours_only",
        smsEnabled: true,
        smsTemplate:
          "Zdravo! Hvala što ste pozvali {businessName}. Možete zakazati termin ovde: {bookingLink}",
      };
    } catch (error) {
      console.error("Error loading settings:", error);
      return null;
    }
  }

  async saveSettings(settings) {
    try {
      await AsyncStorage.setItem("mobileSettings", JSON.stringify(settings));
      this.currentSettings = settings;
      console.log("⚙️ Settings saved:", settings);
    } catch (error) {
      console.error("Error saving settings:", error);
      throw error;
    }
  }

  async testSMS(phoneNumber, message) {
    try {
      console.log("🧪 Testing SMS functionality...");
      return await this.sendSMS(phoneNumber, message);
    } catch (error) {
      console.error("❌ Error testing SMS:", error);
      throw error;
    }
  }

  getServiceStatus() {
    return this.isServiceRunning;
  }

  cleanup() {
    if (this.callDetector) {
      this.callDetector.dispose();
      this.callDetector = null;
    }
    this.isServiceRunning = false;
    console.log("🧹 Service cleaned up");
  }
}

// Export singleton instance
export default new CallInterceptorService();
