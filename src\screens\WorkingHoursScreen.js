import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator,
  StatusBar,
  Platform,
  Modal,
  TextInput,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import ApiService from "../services/api";

const WorkingHoursScreen = () => {
  const [workingHours, setWorkingHours] = useState({
    ponedeljak: { closed: false, from: "09:00", to: "17:00", breaks: [] },
    utorak: { closed: false, from: "09:00", to: "17:00", breaks: [] },
    sreda: { closed: false, from: "09:00", to: "17:00", breaks: [] },
    cetvrtak: { closed: false, from: "09:00", to: "17:00", breaks: [] },
    petak: { closed: false, from: "09:00", to: "17:00", breaks: [] },
    subota: { closed: false, from: "10:00", to: "14:00", breaks: [] },
    nedelja: { closed: true, from: "09:00", to: "17:00", breaks: [] },
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showTimePickerModal, setShowTimePickerModal] = useState(false);
  const [timePickerData, setTimePickerData] = useState({
    day: null,
    field: null,
    value: "",
  });
  const [showBreaksModal, setShowBreaksModal] = useState(false);
  const [editingDay, setEditingDay] = useState(null);
  const [editingBreaks, setEditingBreaks] = useState([]);
  const [currentBreak, setCurrentBreak] = useState({ from: "", to: "" });

  const dayNames = {
    ponedeljak: "Ponedeljak",
    utorak: "Utorak",
    sreda: "Sreda",
    cetvrtak: "Četvrtak",
    petak: "Petak",
    subota: "Subota",
    nedelja: "Nedelja",
  };

  // Lista validnih dana (bez MongoDB _id polja)
  const validDays = [
    "ponedeljak",
    "utorak",
    "sreda",
    "cetvrtak",
    "petak",
    "subota",
    "nedelja",
  ];

  useEffect(() => {
    loadWorkingHours();
  }, []);

  const loadWorkingHours = async (forceRefresh = false) => {
    try {
      setIsLoading(true);

      // If forceRefresh is true, always fetch from API
      if (!forceRefresh) {
        // First try to get working hours from stored user data (from login)
        const storedUserData = await AsyncStorage.getItem("userData");
        if (storedUserData) {
          const userData = JSON.parse(storedUserData);
          if (userData.workingHours) {
            setWorkingHours(userData.workingHours);
            setIsLoading(false);
            return;
          }
        }
      }

      // Fetch from API (either as fallback or forced refresh)
      const data = await ApiService.getWorkingHours();
      if (data.workingHours) {
        setWorkingHours(data.workingHours);

        // Update stored user data with fresh working hours
        const storedUserData = await AsyncStorage.getItem("userData");
        if (storedUserData) {
          const userData = JSON.parse(storedUserData);
          userData.workingHours = data.workingHours;
          await AsyncStorage.setItem("userData", JSON.stringify(userData));
        }
      }
    } catch (error) {
      console.error("Error loading working hours:", error);
      Alert.alert("Greška", "Nije moguće učitati radno vreme");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDayToggle = (day, value) => {
    setWorkingHours((prev) => ({
      ...prev,
      [day]: {
        ...prev[day],
        closed: !value,
      },
    }));
  };

  const handleTimeChange = (day, field, value) => {
    setWorkingHours((prev) => ({
      ...prev,
      [day]: {
        ...prev[day],
        [field]: value,
      },
    }));
  };

  const openTimePicker = (day, field) => {
    setTimePickerData({
      day,
      field,
      value: workingHours[day][field],
    });
    setShowTimePickerModal(true);
  };

  const confirmTimeChange = () => {
    handleTimeChange(
      timePickerData.day,
      timePickerData.field,
      timePickerData.value
    );
    setShowTimePickerModal(false);
  };

  const openBreaksModal = (day) => {
    setEditingDay(day);
    setEditingBreaks(workingHours[day]?.breaks || []);
    setCurrentBreak({ from: "", to: "" });
    setShowBreaksModal(true);
  };

  const handleAddBreak = () => {
    if (!currentBreak.from || !currentBreak.to) {
      Alert.alert("Greška", "Morate definisati početak i kraj pauze");
      return;
    }

    // Provera da li je početak pre kraja
    const fromTime = currentBreak.from.split(":").map(Number);
    const toTime = currentBreak.to.split(":").map(Number);
    const fromMinutes = fromTime[0] * 60 + fromTime[1];
    const toMinutes = toTime[0] * 60 + toTime[1];

    if (fromMinutes >= toMinutes) {
      Alert.alert("Greška", "Početak pauze mora biti pre kraja pauze");
      return;
    }

    // Provera preklapanja sa postojećim pauzama
    const isOverlapping = editingBreaks.some((breakTime) => {
      const breakFromTime = breakTime.from.split(":").map(Number);
      const breakToTime = breakTime.to.split(":").map(Number);
      const breakFromMinutes = breakFromTime[0] * 60 + breakFromTime[1];
      const breakToMinutes = breakToTime[0] * 60 + breakToTime[1];

      return (
        (fromMinutes >= breakFromMinutes && fromMinutes < breakToMinutes) ||
        (toMinutes > breakFromMinutes && toMinutes <= breakToMinutes) ||
        (fromMinutes <= breakFromMinutes && toMinutes >= breakToMinutes)
      );
    });

    if (isOverlapping) {
      Alert.alert("Greška", "Pauza se preklapa sa postojećom pauzom");
      return;
    }

    // Dodaj novu pauzu i sortiraj ih po vremenu početka
    const newBreaks = [...editingBreaks, { ...currentBreak }].sort((a, b) => {
      const aTime = a.from.split(":").map(Number);
      const bTime = b.from.split(":").map(Number);
      const aMinutes = aTime[0] * 60 + aTime[1];
      const bMinutes = bTime[0] * 60 + bTime[1];
      return aMinutes - bMinutes;
    });

    setEditingBreaks(newBreaks);
    setCurrentBreak({ from: "", to: "" });
  };

  const handleDeleteBreak = (index) => {
    const newBreaks = [...editingBreaks];
    newBreaks.splice(index, 1);
    setEditingBreaks(newBreaks);
  };

  const applyBreaksChanges = () => {
    setWorkingHours((prev) => ({
      ...prev,
      [editingDay]: {
        ...prev[editingDay],
        breaks: [...editingBreaks],
      },
    }));
    setShowBreaksModal(false);
  };

  const saveWorkingHours = async () => {
    try {
      setIsSaving(true);

      // Filter out non-day properties like _id (same as web app)
      const dayTranslations = {
        ponedeljak: "ponedeljak",
        utorak: "utorak",
        sreda: "sreda",
        cetvrtak: "cetvrtak",
        petak: "petak",
        subota: "subota",
        nedelja: "nedelja",
      };

      const filteredWorkingHours = {};
      Object.keys(dayTranslations).forEach((day) => {
        if (workingHours[day]) {
          filteredWorkingHours[day] = workingHours[day];
        }
      });

      await ApiService.updateWorkingHours(filteredWorkingHours);
      Alert.alert("Uspeh", "Radno vreme je uspešno ažurirano");

      // Refresh working hours from server
      await loadWorkingHours(true); // Force refresh from API
    } catch (error) {
      console.error("Error saving working hours:", error);
      Alert.alert("Greška", "Nije moguće sačuvati radno vreme");
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2563eb" />
        <Text style={styles.loadingText}>Učitavanje radnog vremena...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Radno vreme</Text>
        <Text style={styles.subtitle}>Podesite radno vreme vašeg salona</Text>
      </View>

      <View style={styles.daysContainer}>
        {validDays.map((day) => {
          // Proveri da li dan postoji u workingHours objektu
          if (!workingHours[day]) return null;

          return (
            <View key={day} style={styles.dayCard}>
              <View style={styles.dayHeader}>
                <Text style={styles.dayName}>{dayNames[day]}</Text>
                <Switch
                  value={!workingHours[day].closed}
                  onValueChange={(value) => handleDayToggle(day, value)}
                  trackColor={{ false: "#d1d5db", true: "#2563eb" }}
                  thumbColor={!workingHours[day].closed ? "#ffffff" : "#f4f3f4"}
                />
              </View>

              {!workingHours[day].closed && (
                <View style={styles.timeContainer}>
                  {/* Radno vreme */}
                  <View style={styles.workingTimeRow}>
                    <View style={styles.timeInputGroup}>
                      <Text style={styles.timeLabel}>Od:</Text>
                      <TouchableOpacity
                        style={styles.timeInput}
                        onPress={() => openTimePicker(day, "from")}
                      >
                        <Text style={styles.timeText}>
                          {workingHours[day].from}
                        </Text>
                      </TouchableOpacity>
                    </View>

                    <View style={styles.timeInputGroup}>
                      <Text style={styles.timeLabel}>Do:</Text>
                      <TouchableOpacity
                        style={styles.timeInput}
                        onPress={() => openTimePicker(day, "to")}
                      >
                        <Text style={styles.timeText}>
                          {workingHours[day].to}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  {/* Sekcija za pauze */}
                  <View style={styles.breaksSection}>
                    <TouchableOpacity
                      style={styles.breaksButton}
                      onPress={() => openBreaksModal(day)}
                    >
                      <Text style={styles.breaksButtonText}>
                        {workingHours[day].breaks &&
                        workingHours[day].breaks.length > 0
                          ? `Pauze (${workingHours[day].breaks.length})`
                          : "Dodaj pauze"}
                      </Text>
                    </TouchableOpacity>

                    {/* Prikaz postojećih pauza */}
                    {workingHours[day].breaks &&
                      workingHours[day].breaks.length > 0 && (
                        <View style={styles.breaksDisplay}>
                          {workingHours[day].breaks.map((breakTime, index) => (
                            <View key={index} style={styles.breakChip}>
                              <Text style={styles.breakChipText}>
                                {breakTime.from} - {breakTime.to}
                              </Text>
                            </View>
                          ))}
                        </View>
                      )}
                  </View>
                </View>
              )}

              {workingHours[day].closed && (
                <Text style={styles.closedText}>Zatvoreno</Text>
              )}
            </View>
          );
        })}
      </View>

      <View style={styles.saveContainer}>
        <TouchableOpacity
          style={[styles.saveButton, isSaving && styles.disabledButton]}
          onPress={saveWorkingHours}
          disabled={isSaving}
        >
          {isSaving ? (
            <ActivityIndicator color="#ffffff" />
          ) : (
            <Text style={styles.saveButtonText}>Sačuvaj radno vreme</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Time Picker Modal */}
      <Modal
        visible={showTimePickerModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowTimePickerModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              Izaberite vreme ({timePickerData.field === "from" ? "Od" : "Do"})
            </Text>

            <TextInput
              style={styles.timePickerInput}
              value={timePickerData.value}
              onChangeText={(text) =>
                setTimePickerData((prev) => ({ ...prev, value: text }))
              }
              placeholder="HH:MM"
              keyboardType="numeric"
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => setShowTimePickerModal(false)}
              >
                <Text style={styles.modalCancelText}>Otkaži</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.modalConfirmButton}
                onPress={confirmTimeChange}
              >
                <Text style={styles.modalConfirmText}>Potvrdi</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Breaks Modal */}
      <Modal
        visible={showBreaksModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowBreaksModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              Pauze za {dayNames[editingDay] || editingDay}
            </Text>

            {/* Dodavanje nove pauze */}
            <View style={styles.addBreakContainer}>
              <Text style={styles.addBreakTitle}>Dodaj novu pauzu:</Text>
              <View style={styles.breakInputRow}>
                <View style={styles.breakInputGroup}>
                  <Text style={styles.breakInputLabel}>Od:</Text>
                  <TextInput
                    style={styles.breakInput}
                    value={currentBreak.from}
                    onChangeText={(text) =>
                      setCurrentBreak((prev) => ({ ...prev, from: text }))
                    }
                    placeholder="HH:MM"
                    keyboardType="numeric"
                  />
                </View>
                <View style={styles.breakInputGroup}>
                  <Text style={styles.breakInputLabel}>Do:</Text>
                  <TextInput
                    style={styles.breakInput}
                    value={currentBreak.to}
                    onChangeText={(text) =>
                      setCurrentBreak((prev) => ({ ...prev, to: text }))
                    }
                    placeholder="HH:MM"
                    keyboardType="numeric"
                  />
                </View>
              </View>
              <TouchableOpacity
                style={styles.addBreakButton}
                onPress={handleAddBreak}
              >
                <Text style={styles.addBreakButtonText}>Dodaj pauzu</Text>
              </TouchableOpacity>
            </View>

            {/* Lista postojećih pauza */}
            <View style={styles.breaksListContainer}>
              {editingBreaks.length === 0 ? (
                <Text style={styles.noBreaksText}>Nema definisanih pauza.</Text>
              ) : (
                editingBreaks.map((breakTime, index) => (
                  <View key={index} style={styles.breakItem}>
                    <Text style={styles.breakItemText}>
                      {breakTime.from} - {breakTime.to}
                    </Text>
                    <TouchableOpacity
                      style={styles.deleteBreakButton}
                      onPress={() => handleDeleteBreak(index)}
                    >
                      <Text style={styles.deleteBreakText}>Ukloni</Text>
                    </TouchableOpacity>
                  </View>
                ))
              )}
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => setShowBreaksModal(false)}
              >
                <Text style={styles.modalCancelText}>Otkaži</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.modalConfirmButton}
                onPress={applyBreaksChanges}
              >
                <Text style={styles.modalConfirmText}>Primeni promene</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
    paddingTop: Platform.OS === "android" ? StatusBar.currentHeight : 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f8f9fa",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#6b7280",
  },
  header: {
    padding: 20,
    backgroundColor: "#ffffff",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: "#6b7280",
  },
  daysContainer: {
    padding: 20,
  },
  dayCard: {
    backgroundColor: "#ffffff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  dayHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  dayName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1f2937",
  },
  timeContainer: {
    marginTop: 12,
  },
  workingTimeRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
    gap: 16,
  },
  timeInputGroup: {
    flex: 1,
    alignItems: "center",
  },
  breaksSection: {
    marginTop: 8,
  },
  timeLabel: {
    fontSize: 14,
    color: "#6b7280",
    marginBottom: 8,
  },
  timeInput: {
    backgroundColor: "#f9fafb",
    borderWidth: 1,
    borderColor: "#d1d5db",
    borderRadius: 8,
    padding: 12,
    minWidth: 80,
    alignItems: "center",
  },
  timeText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1f2937",
  },
  closedText: {
    fontSize: 16,
    color: "#6b7280",
    fontStyle: "italic",
    textAlign: "center",
  },
  saveContainer: {
    padding: 20,
    marginBottom: 20,
  },
  saveButton: {
    backgroundColor: "#2563eb",
    borderRadius: 8,
    padding: 16,
    alignItems: "center",
  },
  disabledButton: {
    backgroundColor: "#9ca3af",
  },
  saveButtonText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "600",
  },
  breaksButton: {
    backgroundColor: "#f3f4f6",
    padding: 10,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "#e5e7eb",
  },
  breaksButtonText: {
    color: "#374151",
    fontSize: 13,
    textAlign: "center",
    fontWeight: "500",
  },
  breaksDisplay: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 8,
    gap: 6,
  },
  breakChip: {
    backgroundColor: "#dbeafe",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#93c5fd",
  },
  breakChipText: {
    fontSize: 11,
    color: "#1e40af",
    fontWeight: "500",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "#ffffff",
    borderRadius: 12,
    padding: 20,
    margin: 20,
    maxHeight: "80%",
    width: "90%",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
    textAlign: "center",
    color: "#1f2937",
  },
  timePickerInput: {
    borderWidth: 1,
    borderColor: "#d1d5db",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
    textAlign: "center",
  },
  modalButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16,
  },
  modalCancelButton: {
    flex: 1,
    backgroundColor: "#f3f4f6",
    padding: 12,
    borderRadius: 8,
    marginRight: 8,
  },
  modalCancelText: {
    color: "#374151",
    textAlign: "center",
    fontSize: 16,
  },
  modalConfirmButton: {
    flex: 1,
    backgroundColor: "#3b82f6",
    padding: 12,
    borderRadius: 8,
    marginLeft: 8,
  },
  modalConfirmText: {
    color: "#ffffff",
    textAlign: "center",
    fontSize: 16,
    fontWeight: "bold",
  },
  addBreakContainer: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: "#f9fafb",
    borderRadius: 8,
  },
  addBreakTitle: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 8,
    color: "#374151",
  },
  breakInputRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  breakInputGroup: {
    flex: 1,
    marginHorizontal: 4,
  },
  breakInputLabel: {
    fontSize: 12,
    color: "#6b7280",
    marginBottom: 4,
  },
  breakInput: {
    borderWidth: 1,
    borderColor: "#d1d5db",
    borderRadius: 6,
    padding: 8,
    fontSize: 14,
    textAlign: "center",
  },
  addBreakButton: {
    backgroundColor: "#10b981",
    padding: 10,
    borderRadius: 6,
  },
  addBreakButtonText: {
    color: "#ffffff",
    textAlign: "center",
    fontSize: 14,
    fontWeight: "600",
  },
  breaksListContainer: {
    maxHeight: 200,
  },
  noBreaksText: {
    textAlign: "center",
    color: "#6b7280",
    fontStyle: "italic",
    padding: 16,
  },
  breakItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
    backgroundColor: "#f3f4f6",
    borderRadius: 6,
    marginBottom: 8,
  },
  breakItemText: {
    fontSize: 14,
    color: "#374151",
  },
  deleteBreakButton: {
    backgroundColor: "#ef4444",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  deleteBreakText: {
    color: "#ffffff",
    fontSize: 12,
    fontWeight: "600",
  },
});

export default WorkingHoursScreen;
