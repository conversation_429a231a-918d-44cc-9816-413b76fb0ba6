import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { View, Text } from "react-native";

import { useAuth } from "../context/AuthContext";

// Auth Screens
import LoginScreen from "../screens/LoginScreen";

// Main App Screens
import DashboardScreen from "../screens/DashboardScreen";
import ProfileScreen from "../screens/ProfileScreen";
import WorkingHoursScreen from "../screens/WorkingHoursScreen";
import ServicesScreen from "../screens/ServicesScreen";
import AppointmentsScreen from "../screens/AppointmentsScreen";
import AIAssistantScreen from "../screens/AIAssistantScreen";
import SimpleSMSScreen from "../screens/SimpleSMSScreen";

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Auth Stack Navigator
const AuthStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerShown: false,
    }}
  >
    <Stack.Screen name="Login" component={LoginScreen} />
  </Stack.Navigator>
);

// Tab Navigator for main app
const MainTabs = () => (
  <Tab.Navigator
    screenOptions={{
      headerShown: false,
      tabBarStyle: {
        backgroundColor: "#ffffff",
        borderTopWidth: 1,
        borderTopColor: "#e5e7eb",
        paddingBottom: 5,
        paddingTop: 5,
        height: 60,
      },
      tabBarActiveTintColor: "#2563eb",
      tabBarInactiveTintColor: "#6b7280",
      tabBarLabelStyle: {
        fontSize: 12,
        fontWeight: "600",
      },
    }}
  >
    <Tab.Screen
      name="Dashboard"
      component={DashboardScreen}
      options={{
        tabBarLabel: "Početna",
        tabBarIcon: ({ color }) => (
          <Text style={{ color, fontSize: 20 }}>📊</Text>
        ),
      }}
    />
    <Tab.Screen
      name="Appointments"
      component={AppointmentsScreen}
      options={{
        tabBarLabel: "Zakazivanja",
        tabBarIcon: ({ color }) => (
          <Text style={{ color, fontSize: 20 }}>📅</Text>
        ),
      }}
    />
    <Tab.Screen
      name="AIAssistant"
      component={AIAssistantScreen}
      options={{
        tabBarLabel: "AI Asistent",
        tabBarIcon: ({ color }) => (
          <Text style={{ color, fontSize: 20 }}>🤖</Text>
        ),
      }}
    />
    <Tab.Screen
      name="Services"
      component={ServicesScreen}
      options={{
        tabBarLabel: "Usluge",
        tabBarIcon: ({ color }) => (
          <Text style={{ color, fontSize: 20 }}>💼</Text>
        ),
      }}
    />
    <Tab.Screen
      name="Profile"
      component={ProfileScreen}
      options={{
        tabBarLabel: "Profil",
        tabBarIcon: ({ color }) => (
          <Text style={{ color, fontSize: 20 }}>👤</Text>
        ),
      }}
    />
  </Tab.Navigator>
);

// Main App Stack Navigator
const AppStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerShown: false,
    }}
  >
    <Stack.Screen name="MainTabs" component={MainTabs} />
    <Stack.Screen
      name="WorkingHours"
      component={WorkingHoursScreen}
      options={{
        headerShown: true,
        title: "Radno vreme",
        headerStyle: {
          backgroundColor: "#2563eb",
        },
        headerTintColor: "#ffffff",
        headerTitleStyle: {
          fontWeight: "bold",
        },
      }}
    />
    <Stack.Screen
      name="MobileSettings"
      component={SimpleSMSScreen}
      options={{
        headerShown: true,
        title: "SMS Servis",
        headerStyle: {
          backgroundColor: "#2563eb",
        },
        headerTintColor: "#ffffff",
        headerTitleStyle: {
          fontWeight: "bold",
        },
      }}
    />
  </Stack.Navigator>
);

// Loading Screen Component
const LoadingScreen = () => (
  <View
    style={{
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "#f8f9fa",
    }}
  >
    <Text style={{ fontSize: 18, color: "#6b7280" }}>Učitavanje...</Text>
  </View>
);

// Main App Navigator
const AppNavigator = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      {isAuthenticated ? <AppStack /> : <AuthStack />}
    </NavigationContainer>
  );
};

export default AppNavigator;
