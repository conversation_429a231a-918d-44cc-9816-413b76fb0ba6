import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  SafeAreaView,
  StatusBar,
  Platform,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import ApiService from "../services/api";
import { LineChart, BarChart } from "react-native-chart-kit";

const AIAssistantScreen = () => {
  const [userData, setUserData] = useState(null);
  const [analysis, setAnalysis] = useState(null);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("competitive");
  const [dateRange, setDateRange] = useState("30days");
  const [error, setError] = useState("");
  const [cached, setCached] = useState(false);
  const [analysisCache, setAnalysisCache] = useState({});

  const screenWidth = Dimensions.get("window").width;

  const chartConfig = {
    backgroundColor: "#ffffff",
    backgroundGradientFrom: "#ffffff",
    backgroundGradientTo: "#ffffff",
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(37, 99, 235, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(31, 41, 55, ${opacity})`,
    style: {
      borderRadius: 8,
    },
    propsForDots: {
      r: "4",
      strokeWidth: "2",
      stroke: "#2563eb",
    },
  };

  const analysisTypes = [
    { id: "competitive", name: "Konkurentska analiza", icon: "🏆" },
    { id: "occupancy", name: "Zauzetost", icon: "📅" },
    { id: "revenue", name: "Prihodi", icon: "💰" },
    { id: "cancellation", name: "Otkazivanja", icon: "❌" },
  ];

  useEffect(() => {
    loadUserData();
  }, []);

  useEffect(() => {
    if (userData && hasAIAccess()) {
      loadAnalysis();
    }
  }, [activeTab, dateRange, userData]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadUserData = async () => {
    try {
      const storedUserData = await AsyncStorage.getItem("userData");
      if (storedUserData) {
        const parsedData = JSON.parse(storedUserData);
        setUserData(parsedData);
      }
    } catch (error) {
      console.error("Error loading user data:", error);
    }
  };

  const hasAIAccess = () => {
    if (!userData) return false;
    const userPlan = userData.plan || "standard";
    return userPlan !== "standard"; // AI analitika dostupna za standard-plus, premium, enterprise
  };

  // Funkcija za proveru da li je potrebno ažuriranje
  const shouldRefreshData = (timestamp) => {
    if (!timestamp) return true;

    const now = new Date();
    const lastUpdate = new Date(timestamp);

    // Pronađi poslednji ponedeljak u 9:00
    const lastMonday = new Date(now);
    lastMonday.setDate(now.getDate() - ((now.getDay() + 6) % 7)); // Idi na poslednji ponedeljak
    lastMonday.setHours(9, 0, 0, 0);

    // Ako je danas ponedeljak i još nije 9:00, koristi prošli ponedeljak
    if (now.getDay() === 1 && now.getHours() < 9) {
      lastMonday.setDate(lastMonday.getDate() - 7);
    }

    return lastUpdate < lastMonday;
  };

  const loadAnalysis = async (forceRefresh = false) => {
    if (!userData || !hasAIAccess()) return;

    const cacheKey = `${activeTab}-${dateRange}`;

    // Proveri cache i da li je potrebno ažuriranje
    if (analysisCache[cacheKey] && !forceRefresh) {
      const cachedData = analysisCache[cacheKey];

      // Ako podaci nisu stari (pre poslednjeg ponedeljka u 9:00), koristi ih
      if (!shouldRefreshData(cachedData.timestamp)) {
        setAnalysis(cachedData.analysis);
        setStats(cachedData.stats);
        setCached(true);
        setLoading(false);
        return;
      }
    }

    setLoading(true);
    setError("");
    setCached(false);

    try {
      // Use ApiService to make the request
      const data = await ApiService.makeRequest("/api/ai/analytics", {
        method: "POST",
        body: JSON.stringify({
          userId: userData._id,
          analysisType: activeTab,
          dateRange: dateRange,
          forceRefresh:
            forceRefresh ||
            shouldRefreshData(analysisCache[cacheKey]?.timestamp),
        }),
      });

      setAnalysis(data.analysis);
      setStats(data.stats);
      setCached(data.cached || false);

      // Sačuvaj u lokalni cache sa trenutnim timestamp-om
      setAnalysisCache((prev) => ({
        ...prev,
        [cacheKey]: {
          analysis: data.analysis,
          stats: data.stats,
          timestamp: Date.now(),
        },
      }));
    } catch (error) {
      console.error("Error loading AI analysis:", error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Uklonjen onRefresh - nema više pull-to-refresh

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const handleDateRangeChange = (newDateRange) => {
    setDateRange(newDateRange);
  };

  const renderChart = () => {
    if (!analysis || !analysis.trendData || analysis.trendData.length === 0) {
      return null;
    }

    const chartWidth = screenWidth - 32; // Account for padding

    switch (activeTab) {
      case "occupancy":
        return (
          <BarChart
            data={{
              labels: analysis.trendData.map((item) => item.period),
              datasets: [
                {
                  data: analysis.trendData.map((item) => item.occupancy || 0),
                  color: (opacity = 1) => `rgba(59, 130, 246, ${opacity})`,
                },
              ],
            }}
            width={chartWidth}
            height={220}
            chartConfig={chartConfig}
            style={styles.chart}
          />
        );

      case "revenue":
        return (
          <LineChart
            data={{
              labels: analysis.trendData.map((item) => item.period),
              datasets: [
                {
                  data: analysis.trendData.map((item) => item.revenue || 0),
                  color: (opacity = 1) => `rgba(59, 130, 246, ${opacity})`,
                  strokeWidth: 2,
                },
              ],
            }}
            width={chartWidth}
            height={220}
            chartConfig={chartConfig}
            style={styles.chart}
          />
        );

      case "cancellation":
        return (
          <BarChart
            data={{
              labels: analysis.trendData.map((item) => item.period),
              datasets: [
                {
                  data: analysis.trendData.map(
                    (item) => item.cancellations || 0
                  ),
                  color: (opacity = 1) => `rgba(239, 68, 68, ${opacity})`,
                },
              ],
            }}
            width={chartWidth}
            height={220}
            chartConfig={{
              ...chartConfig,
              color: (opacity = 1) => `rgba(239, 68, 68, ${opacity})`,
            }}
            style={styles.chart}
          />
        );

      case "competitive":
      default:
        return (
          <BarChart
            data={{
              labels: analysis.trendData.map((item) => item.period),
              datasets: [
                {
                  data: analysis.trendData.map((item) => item.yourSalon || 0),
                  color: (opacity = 1) => `rgba(59, 130, 246, ${opacity})`,
                },
              ],
            }}
            width={chartWidth}
            height={220}
            chartConfig={chartConfig}
            style={styles.chart}
          />
        );
    }
  };

  // Ako nema podataka o korisniku, prikaži loading
  if (!userData) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>AI Analitika</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2563eb" />
          <Text style={styles.loadingText}>Učitavam podatke...</Text>
        </View>
      </View>
    );
  }

  // Provera plana - ako nema pristup AI analitici
  if (!hasAIAccess()) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>AI Analitika</Text>
        </View>
        <ScrollView style={styles.content}>
          <View style={styles.noAccessContainer}>
            <Text style={styles.noAccessIcon}>🤖</Text>
            <Text style={styles.noAccessTitle}>AI Analitika</Text>
            <Text style={styles.noAccessDescription}>
              AI analitika je dostupna samo za Standard Plus plan i više.
            </Text>
            <View style={styles.upgradeContainer}>
              <Text style={styles.upgradeTitle}>
                Nadogradite na Standard Plus plan da biste dobili:
              </Text>
              <View style={styles.featuresList}>
                <Text style={styles.featureItem}>
                  • Automatske preporuke za optimalno radno vreme
                </Text>
                <Text style={styles.featureItem}>
                  • Analizu obrazaca zauzetosti
                </Text>
                <Text style={styles.featureItem}>
                  • Naprednu statistiku i trendove
                </Text>
                <Text style={styles.featureItem}>
                  • Preporuke za povećanje prihoda
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  // Glavni UI za AI analitiku
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>AI Analitika</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* Analysis Type Tabs */}
        <View style={styles.tabsContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {analysisTypes.map((type) => (
              <TouchableOpacity
                key={type.id}
                style={[styles.tab, activeTab === type.id && styles.activeTab]}
                onPress={() => handleTabChange(type.id)}
              >
                <Text style={styles.tabIcon}>{type.icon}</Text>
                <Text
                  style={[
                    styles.tabText,
                    activeTab === type.id && styles.activeTabText,
                  ]}
                >
                  {type.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Date Range Selector */}
        <View style={styles.dateRangeContainer}>
          <Text style={styles.dateRangeLabel}>Period:</Text>
          <View style={styles.dateRangeButtons}>
            <TouchableOpacity
              style={[
                styles.dateRangeButton,
                dateRange === "7days" && styles.activeDateRangeButton,
              ]}
              onPress={() => handleDateRangeChange("7days")}
            >
              <Text
                style={[
                  styles.dateRangeButtonText,
                  dateRange === "7days" && styles.activeDateRangeButtonText,
                ]}
              >
                7 dana
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.dateRangeButton,
                dateRange === "30days" && styles.activeDateRangeButton,
              ]}
              onPress={() => handleDateRangeChange("30days")}
            >
              <Text
                style={[
                  styles.dateRangeButtonText,
                  dateRange === "30days" && styles.activeDateRangeButtonText,
                ]}
              >
                30 dana
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Stats Cards */}
        {stats && (
          <View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{stats.totalBookings}</Text>
              <Text style={styles.statLabel}>Ukupno termina</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={[styles.statNumber, { color: "#10b981" }]}>
                {stats.confirmedBookings}
              </Text>
              <Text style={styles.statLabel}>Potvrđeno</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={[styles.statNumber, { color: "#8b5cf6" }]}>
                {stats.completedBookings}
              </Text>
              <Text style={styles.statLabel}>Završeno</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={[styles.statNumber, { color: "#ef4444" }]}>
                {stats.cancellationRate}%
              </Text>
              <Text style={styles.statLabel}>Stopa otkazivanja</Text>
            </View>
          </View>
        )}

        {/* Chart */}
        {analysis && analysis.trendData && analysis.trendData.length > 0 && (
          <View style={styles.chartContainer}>
            <Text style={styles.chartTitle}>Trendovi i podaci</Text>
            {renderChart()}
          </View>
        )}

        {/* Error */}
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        {/* Loading */}
        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#2563eb" />
            <Text style={styles.loadingText}>AI analizira vaše podatke...</Text>
          </View>
        )}

        {/* Analysis Results */}
        {analysis && !loading && (
          <View style={styles.analysisContainer}>
            <View style={styles.analysisHeader}>
              <Text style={styles.analysisIcon}>
                {analysisTypes.find((t) => t.id === activeTab)?.icon || "🏆"}
              </Text>
              <Text style={styles.analysisTitle}>
                {analysisTypes.find((t) => t.id === activeTab)?.name ||
                  "Analiza"}
              </Text>
            </View>

            <Text style={styles.analysisText}>
              {typeof analysis === "string" ? analysis : analysis.summaryText}
            </Text>

            {/* Recommendations */}
            {analysis.recommendations &&
              analysis.recommendations.length > 0 && (
                <View style={styles.recommendationsContainer}>
                  <Text style={styles.recommendationsTitle}>Preporuke</Text>
                  {analysis.recommendations.map((rec, index) => (
                    <View key={index} style={styles.recommendationItem}>
                      <Text style={styles.recommendationBullet}>•</Text>
                      <Text style={styles.recommendationText}>
                        {rec.text || rec}
                      </Text>
                    </View>
                  ))}
                </View>
              )}
          </View>
        )}

        {/* Cache Info */}
        {cached && (
          <View style={styles.cacheInfoContainer}>
            <Text style={styles.cacheInfoText}>
              📋 Podaci se ažuriraju svakog ponedeljka u 9:00.
            </Text>
          </View>
        )}

        {/* Tips */}
        <View style={styles.tipsContainer}>
          <View style={styles.tipsHeader}>
            <Text style={styles.tipsIcon}>💡</Text>
            <Text style={styles.tipsTitle}>
              Saveti za korišćenje AI analitike
            </Text>
          </View>
          <View style={styles.tipsList}>
            <Text style={styles.tipItem}>
              • <Text style={styles.tipBold}>Konkurentska analiza:</Text>{" "}
              Poredite se sa konkurencijom i dobijte preporuke za poboljšanje
            </Text>
            <Text style={styles.tipItem}>
              • <Text style={styles.tipBold}>Zauzetost:</Text> Optimizujte radno
              vreme na osnovu obrazaca
            </Text>
            <Text style={styles.tipItem}>
              • <Text style={styles.tipBold}>Prihodi:</Text> Analizirajte
              najtraženije usluge i potencijal
            </Text>
            <Text style={styles.tipItem}>
              • <Text style={styles.tipBold}>Otkazivanja:</Text> Smanjite stopu
              otkazivanja uz AI preporuke
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
    paddingTop: Platform.OS === "android" ? StatusBar.currentHeight : 0,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    backgroundColor: "#ffffff",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1f2937",
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#6b7280",
    textAlign: "center",
  },
  noAccessContainer: {
    backgroundColor: "#ffffff",
    padding: 24,
    borderRadius: 12,
    alignItems: "center",
    marginBottom: 16,
  },
  noAccessIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  noAccessTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 8,
  },
  noAccessDescription: {
    fontSize: 16,
    color: "#6b7280",
    textAlign: "center",
    marginBottom: 16,
  },
  upgradeContainer: {
    backgroundColor: "#fef3c7",
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#fbbf24",
    width: "100%",
  },
  upgradeTitle: {
    fontSize: 14,
    color: "#92400e",
    marginBottom: 8,
  },
  featuresList: {
    gap: 4,
  },
  featureItem: {
    fontSize: 14,
    color: "#b45309",
  },
  tabsContainer: {
    marginBottom: 16,
  },
  tab: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#ffffff",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#e5e7eb",
  },
  activeTab: {
    backgroundColor: "#2563eb",
    borderColor: "#2563eb",
  },
  tabIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  tabText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#374151",
  },
  activeTabText: {
    color: "#ffffff",
  },
  dateRangeContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
    backgroundColor: "#ffffff",
    padding: 16,
    borderRadius: 8,
  },
  dateRangeLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: "#374151",
  },
  dateRangeButtons: {
    flexDirection: "row",
    gap: 8,
  },
  dateRangeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: "#f3f4f6",
    borderWidth: 1,
    borderColor: "#d1d5db",
  },
  activeDateRangeButton: {
    backgroundColor: "#10b981",
    borderColor: "#10b981",
  },
  dateRangeButtonText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#374151",
  },
  activeDateRangeButtonText: {
    color: "#ffffff",
  },
  statsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    minWidth: "45%",
    backgroundColor: "#ffffff",
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#2563eb",
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: "#6b7280",
    textAlign: "center",
  },
  errorContainer: {
    backgroundColor: "#fef2f2",
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#fecaca",
    marginBottom: 16,
  },
  errorText: {
    color: "#dc2626",
    fontSize: 14,
  },
  analysisContainer: {
    backgroundColor: "#ffffff",
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  analysisHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  analysisIcon: {
    fontSize: 24,
    marginRight: 8,
  },
  analysisTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1f2937",
  },
  analysisText: {
    fontSize: 16,
    color: "#374151",
    lineHeight: 24,
    marginBottom: 16,
  },
  recommendationsContainer: {
    marginTop: 16,
  },
  recommendationsTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 8,
  },
  recommendationItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  recommendationBullet: {
    color: "#10b981",
    fontSize: 16,
    marginRight: 8,
    marginTop: 2,
  },
  recommendationText: {
    flex: 1,
    fontSize: 14,
    color: "#374151",
    lineHeight: 20,
  },
  tipsContainer: {
    backgroundColor: "#ffffff",
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  tipsHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  tipsIcon: {
    fontSize: 24,
    marginRight: 8,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1f2937",
  },
  tipsList: {
    gap: 8,
  },
  tipItem: {
    fontSize: 14,
    color: "#6b7280",
    lineHeight: 20,
  },
  tipBold: {
    fontWeight: "bold",
    color: "#374151",
  },
  chartContainer: {
    backgroundColor: "#ffffff",
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 12,
  },
  chart: {
    borderRadius: 8,
  },
  cacheInfoContainer: {
    backgroundColor: "#f0f9ff",
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#bae6fd",
  },
  cacheInfoText: {
    fontSize: 12,
    color: "#0369a1",
    textAlign: "center",
  },
});

export default AIAssistantScreen;
