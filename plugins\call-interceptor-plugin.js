const { withAndroidManifest, withMainApplication } = require('@expo/config-plugins');

const withCallInterceptor = (config) => {
  // Add Android permissions
  config = withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;
    
    const permissions = [
      'android.permission.CALL_PHONE',
      'android.permission.READ_PHONE_STATE',
      'android.permission.ANSWER_PHONE_CALLS',
      'android.permission.SEND_SMS',
      'android.permission.FOREGROUND_SERVICE',
      'android.permission.WAKE_LOCK',
      'android.permission.RECEIVE_BOOT_COMPLETED'
    ];

    permissions.forEach(permission => {
      if (!androidManifest.manifest['uses-permission']?.find(p => p.$['android:name'] === permission)) {
        androidManifest.manifest['uses-permission'] = [
          ...(androidManifest.manifest['uses-permission'] || []),
          { $: { 'android:name': permission } }
        ];
      }
    });

    // Add service declaration
    const application = androidManifest.manifest.application[0];
    application.service = [
      ...(application.service || []),
      {
        $: {
          'android:name': '.CallInterceptorService',
          'android:enabled': 'true',
          'android:exported': 'false',
          'android:foregroundServiceType': 'phoneCall'
        }
      }
    ];

    return config;
  });

  // Register native module
  config = withMainApplication(config, (config) => {
    const mainApplication = config.modResults;
    
    // Add import
    if (!mainApplication.contents.includes('import com.zakaziai.CallInterceptorPackage;')) {
      mainApplication.contents = mainApplication.contents.replace(
        /import com\.facebook\.react\.ReactApplication;/,
        `import com.facebook.react.ReactApplication;
import com.zakaziai.CallInterceptorPackage;`
      );
    }

    // Add package to getPackages
    if (!mainApplication.contents.includes('new CallInterceptorPackage()')) {
      mainApplication.contents = mainApplication.contents.replace(
        /new MainReactPackage\(\)/,
        `new MainReactPackage(),
            new CallInterceptorPackage()`
      );
    }

    return config;
  });

  return config;
};

module.exports = withCallInterceptor;