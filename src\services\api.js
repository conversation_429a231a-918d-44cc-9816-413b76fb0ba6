// API service for Zakazi AI mobile app
import AsyncStorage from "@react-native-async-storage/async-storage";

// Base URL for your existing backend
const BASE_URL = "https://zakazi-ai.vercel.app";

class ApiService {
  constructor() {
    this.baseURL = BASE_URL;
  }

  // Get stored auth token
  async getAuthToken() {
    try {
      return await AsyncStorage.getItem("authToken");
    } catch (error) {
      console.error("Error getting auth token:", error);
      return null;
    }
  }

  // Store auth token
  async setAuthToken(token) {
    try {
      await AsyncStorage.setItem("authToken", token);
    } catch (error) {
      console.error("Error storing auth token:", error);
    }
  }

  // Remove auth token
  async removeAuthToken() {
    try {
      await AsyncStorage.removeItem("authToken");
    } catch (error) {
      console.error("Error removing auth token:", error);
    }
  }

  // Make authenticated API request
  async makeRequest(endpoint, options = {}) {
    const token = await this.getAuthToken();

    const config = {
      headers: {
        "Content-Type": "application/json",
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, config);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("API request failed:", error);
      throw error;
    }
  }

  // Authentication methods
  async login(email, password) {
    const response = await this.makeRequest("/api/login", {
      method: "POST",
      body: JSON.stringify({ email, password }),
    });

    if (response.token) {
      await this.setAuthToken(response.token);
    }

    // Store user data for later use
    if (response.user) {
      await AsyncStorage.setItem("userData", JSON.stringify(response.user));
    }

    return response;
  }

  async register(userData) {
    const response = await this.makeRequest("/api/register", {
      method: "POST",
      body: JSON.stringify(userData),
    });

    if (response.token) {
      await this.setAuthToken(response.token);
    }

    return response;
  }

  async logout() {
    await this.removeAuthToken();
  }

  // User data methods
  async getCurrentUser() {
    return await this.makeRequest("/api/me");
  }

  async updateUser(userData) {
    try {
      // Get user ID from stored data
      const storedUserData = await AsyncStorage.getItem("userData");
      if (!storedUserData) {
        throw new Error("No user data found - please login again");
      }

      const parsedData = JSON.parse(storedUserData);
      const userId = parsedData._id || parsedData.id;

      if (!userId) {
        throw new Error("User ID not found - please login again");
      }

      return await this.makeRequest("/api/update-user", {
        method: "PUT",
        body: JSON.stringify({
          userId,
          userData,
        }),
      });
    } catch (error) {
      console.error("Error updating user:", error);
      throw error;
    }
  }

  // Working hours methods
  async getWorkingHours() {
    try {
      // Get user ID from stored data
      const storedUserData = await AsyncStorage.getItem("userData");
      if (!storedUserData) {
        throw new Error("No user data found - please login again");
      }

      const parsedData = JSON.parse(storedUserData);
      const userId = parsedData._id || parsedData.id;

      if (!userId) {
        throw new Error("User ID not found - please login again");
      }

      // Use same endpoint as web app
      return await this.makeRequest(`/api/working-hours/get?userId=${userId}`);
    } catch (error) {
      console.error("Error getting working hours:", error);
      throw error;
    }
  }

  async updateWorkingHours(workingHours) {
    try {
      // Get user ID from stored data
      const storedUserData = await AsyncStorage.getItem("userData");
      if (!storedUserData) {
        throw new Error("No user data found - please login again");
      }

      const parsedData = JSON.parse(storedUserData);
      const userId = parsedData._id || parsedData.id;

      if (!userId) {
        throw new Error("User ID not found - please login again");
      }

      // Use same endpoint as web app
      return await this.makeRequest("/api/working-hours/update", {
        method: "POST",
        body: JSON.stringify({
          userId,
          workingHours,
        }),
      });
    } catch (error) {
      console.error("Error updating working hours:", error);
      throw error;
    }
  }

  // Services methods
  async getServices() {
    try {
      // Get user ID from stored data
      const storedUserData = await AsyncStorage.getItem("userData");
      if (!storedUserData) {
        throw new Error("No user data found - please login again");
      }

      const parsedData = JSON.parse(storedUserData);
      const userId = parsedData._id || parsedData.id;

      if (!userId) {
        throw new Error("User ID not found - please login again");
      }

      // Use same endpoint as web app
      return await this.makeRequest(`/api/services/get-all?userId=${userId}`);
    } catch (error) {
      console.error("Error getting services:", error);
      throw error;
    }
  }

  async createService(serviceData) {
    try {
      // Get user ID from stored data
      const storedUserData = await AsyncStorage.getItem("userData");
      if (!storedUserData) {
        throw new Error("No user data found - please login again");
      }

      const parsedData = JSON.parse(storedUserData);
      const userId = parsedData._id || parsedData.id;

      if (!userId) {
        throw new Error("User ID not found - please login again");
      }

      return await this.makeRequest("/api/services/create", {
        method: "POST",
        body: JSON.stringify({
          userId,
          ...serviceData,
        }),
      });
    } catch (error) {
      console.error("Error creating service:", error);
      throw error;
    }
  }

  async updateService(serviceData) {
    try {
      // Get user ID from stored data
      const storedUserData = await AsyncStorage.getItem("userData");
      if (!storedUserData) {
        throw new Error("No user data found - please login again");
      }

      const parsedData = JSON.parse(storedUserData);
      const userId = parsedData._id || parsedData.id;

      if (!userId) {
        throw new Error("User ID not found - please login again");
      }

      return await this.makeRequest("/api/services/update", {
        method: "POST",
        body: JSON.stringify({
          userId,
          ...serviceData,
        }),
      });
    } catch (error) {
      console.error("Error updating service:", error);
      throw error;
    }
  }

  async deleteService(serviceId) {
    try {
      // Get user ID from stored data
      const storedUserData = await AsyncStorage.getItem("userData");
      if (!storedUserData) {
        throw new Error("No user data found - please login again");
      }

      const parsedData = JSON.parse(storedUserData);
      const userId = parsedData._id || parsedData.id;

      if (!userId) {
        throw new Error("User ID not found - please login again");
      }

      return await this.makeRequest("/api/services/delete", {
        method: "DELETE",
        body: JSON.stringify({
          userId,
          serviceId,
        }),
      });
    } catch (error) {
      console.error("Error deleting service:", error);
      throw error;
    }
  }

  // Bookings methods
  async getBookings() {
    try {
      // Get user ID from login response stored in AsyncStorage
      const storedUserData = await AsyncStorage.getItem("userData");
      if (!storedUserData) {
        throw new Error("No user data found - please login again");
      }

      const parsedData = JSON.parse(storedUserData);
      const userId = parsedData._id || parsedData.id;

      if (!userId) {
        throw new Error(
          "User ID not found in stored data - please login again"
        );
      }

      // Get bookings with userId parameter (same as web app)
      const response = await this.makeRequest(`/api/bookings?userId=${userId}`);

      return response;
    } catch (error) {
      console.error("Error getting bookings:", error);
      throw error;
    }
  }

  async updateBookingStatus(bookingId, status) {
    try {
      // Get user ID from stored data
      const storedUserData = await AsyncStorage.getItem("userData");
      if (!storedUserData) {
        throw new Error("No user data found - please login again");
      }

      const parsedData = JSON.parse(storedUserData);
      const userId = parsedData._id || parsedData.id;

      if (!userId) {
        throw new Error("User ID not found - please login again");
      }

      return await this.makeRequest("/api/bookings/update-status", {
        method: "POST",
        body: JSON.stringify({
          userId,
          bookingId,
          status,
        }),
      });
    } catch (error) {
      console.error("Error updating booking status:", error);
      throw error;
    }
  }

  async deleteBooking(bookingId) {
    try {
      // Get user ID from stored data
      const storedUserData = await AsyncStorage.getItem("userData");
      if (!storedUserData) {
        throw new Error("No user data found - please login again");
      }

      const parsedData = JSON.parse(storedUserData);
      const userId = parsedData._id || parsedData.id;

      if (!userId) {
        throw new Error("User ID not found - please login again");
      }

      return await this.makeRequest("/api/bookings/delete", {
        method: "DELETE",
        body: JSON.stringify({
          userId,
          bookingId,
        }),
      });
    } catch (error) {
      console.error("Error deleting booking:", error);
      throw error;
    }
  }

  // AI methods
  async sendAIMessage(message, context = {}) {
    return await this.makeRequest("/api/ai/booking-assistant", {
      method: "POST",
      body: JSON.stringify({ message, context }),
    });
  }

  async getAIAnalytics(analysisType, dateRange) {
    return await this.makeRequest("/api/ai/analytics", {
      method: "POST",
      body: JSON.stringify({ analysisType, dateRange }),
    });
  }
}

export default new ApiService();
