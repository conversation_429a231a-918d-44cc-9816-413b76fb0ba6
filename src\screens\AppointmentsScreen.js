import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Modal,
  SafeAreaView,
  StatusBar,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import DateTimePicker from "@react-native-community/datetimepicker";
import ApiService from "../services/api";

const AppointmentsScreen = () => {
  const [appointments, setAppointments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [userData, setUserData] = useState(null);
  const [services, setServices] = useState([]);
  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    serviceId: "",
    date: "",
    time: "",
  });
  const [availableTimeSlots, setAvailableTimeSlots] = useState([]);
  const [isSaving, setIsSaving] = useState(false);
  const [filter, setFilter] = useState("current"); // all, pending, confirmed, completed, cancelled
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());

  useEffect(() => {
    loadAppointments();
    loadUserData();
    loadServices();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Ažuriranje dostupnih termina kada se datum promeni
  useEffect(() => {
    if (formData.date && userData) {
      const date = new Date(formData.date);
      const dayOfWeek = date.getDay();
      const dayName = dayNameMapping[dayOfWeek];

      const workingHours = userData.workingHours;
      if (workingHours && workingHours[dayName]) {
        const daySchedule = workingHours[dayName];
        const timeSlots = generateTimeSlotsForDay(daySchedule);
        setAvailableTimeSlots(timeSlots);

        if (timeSlots.length === 0) {
          setFormData((prev) => ({ ...prev, time: "" }));
        }
      } else {
        setAvailableTimeSlots([]);
        setFormData((prev) => ({ ...prev, time: "" }));
      }
    }
  }, [formData.date, userData]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleFormChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const resetForm = () => {
    setFormData({
      name: "",
      phone: "",
      serviceId: services.length > 0 ? services[0]._id : "",
      date: "",
      time: "",
    });
    setAvailableTimeSlots([]);
    setSelectedDate(new Date());
  };

  const handleDateChange = (event, date) => {
    setShowDatePicker(false);
    if (date) {
      setSelectedDate(date);
      const formattedDate = date.toISOString().split("T")[0]; // YYYY-MM-DD format
      handleFormChange("date", formattedDate);
    }
  };

  const formatDisplayDate = (dateString) => {
    if (!dateString) return "Izaberite datum";
    const date = new Date(dateString);

    // Imena dana na srpskom
    const dayNames = [
      "nedelja",
      "ponedeljak",
      "utorak",
      "sreda",
      "četvrtak",
      "petak",
      "subota",
    ];

    // Imena meseci na srpskom
    const monthNames = [
      "januar",
      "februar",
      "mart",
      "april",
      "maj",
      "jun",
      "jul",
      "avgust",
      "septembar",
      "oktobar",
      "novembar",
      "decembar",
    ];

    const dayName = dayNames[date.getDay()];
    const day = date.getDate();
    const month = monthNames[date.getMonth()];
    const year = date.getFullYear();

    return `${dayName}, ${day}. ${month} ${year}.`;
  };

  const handleSubmitBooking = async () => {
    if (
      !formData.name ||
      !formData.phone ||
      !formData.serviceId ||
      !formData.date ||
      !formData.time
    ) {
      Alert.alert("Greška", "Molimo popunite sva polja");
      return;
    }

    setIsSaving(true);
    try {
      const response = await ApiService.makeRequest("/api/book", {
        method: "POST",
        body: JSON.stringify({
          ...formData,
          userId: userData._id,
        }),
      });

      Alert.alert("Uspeh", "Termin je uspešno zakazan!", [
        {
          text: "OK",
          onPress: () => {
            resetForm();
            setShowForm(false);
            loadAppointments(); // Refresh appointments list
          },
        },
      ]);
    } catch (error) {
      Alert.alert(
        "Greška",
        error.message || "Greška prilikom zakazivanja termina"
      );
    } finally {
      setIsSaving(false);
    }
  };

  const loadUserData = async () => {
    try {
      const storedUserData = await AsyncStorage.getItem("userData");
      if (storedUserData) {
        setUserData(JSON.parse(storedUserData));
      }
    } catch (error) {
      console.error("Error loading user data:", error);
    }
  };

  const loadServices = async () => {
    try {
      const response = await ApiService.getServices();
      setServices(response.services || []);

      // Set default service if available
      if (
        response.services &&
        response.services.length > 0 &&
        !formData.serviceId
      ) {
        setFormData((prev) => ({
          ...prev,
          serviceId: response.services[0]._id,
        }));
      }
    } catch (error) {
      console.error("Error loading services:", error);
    }
  };

  // Prevođenje imena dana na srpski
  const dayNameMapping = {
    0: "nedelja",
    1: "ponedeljak",
    2: "utorak",
    3: "sreda",
    4: "cetvrtak",
    5: "petak",
    6: "subota",
  };

  // Generisanje dostupnih termina na osnovu radnog vremena
  const generateTimeSlotsForDay = (daySchedule) => {
    if (!daySchedule || daySchedule.closed) return [];
    if (!daySchedule.from || !daySchedule.to) return [];

    const slots = [];
    const interval = 30; // 30 minuta između termina

    try {
      const [fromHour, fromMinute] = (daySchedule.from || "9:00")
        .split(":")
        .map(Number);
      const [toHour, toMinute] = (daySchedule.to || "17:00")
        .split(":")
        .map(Number);

      if (
        isNaN(fromHour) ||
        isNaN(fromMinute) ||
        isNaN(toHour) ||
        isNaN(toMinute)
      ) {
        return [];
      }

      const fromMinutes = fromHour * 60 + fromMinute;
      const toMinutes = toHour * 60 + toMinute;

      for (let mins = fromMinutes; mins < toMinutes; mins += interval) {
        const hour = Math.floor(mins / 60);
        const minute = mins % 60;
        const timeStr = `${hour}:${minute === 0 ? "00" : minute}`;

        // Provera da li je vreme u pauzi
        let isInBreak = false;
        if (daySchedule.breaks && Array.isArray(daySchedule.breaks)) {
          isInBreak = daySchedule.breaks.some((breakTime) => {
            if (!breakTime || !breakTime.from || !breakTime.to) return false;

            try {
              const [breakFromHour, breakFromMinute] = breakTime.from
                .split(":")
                .map(Number);
              const [breakToHour, breakToMinute] = breakTime.to
                .split(":")
                .map(Number);

              if (
                isNaN(breakFromHour) ||
                isNaN(breakFromMinute) ||
                isNaN(breakToHour) ||
                isNaN(breakToMinute)
              ) {
                return false;
              }

              const breakFromMinutes = breakFromHour * 60 + breakFromMinute;
              const breakToMinutes = breakToHour * 60 + breakToMinute;

              return mins >= breakFromMinutes && mins < breakToMinutes;
            } catch (err) {
              return false;
            }
          });
        }

        if (!isInBreak) {
          slots.push(timeStr);
        }
      }

      return slots;
    } catch (err) {
      return [];
    }
  };

  const loadAppointments = async () => {
    try {
      setIsLoading(true);
      const data = await ApiService.getBookings();
      setAppointments(data.bookings || []);
    } catch (error) {
      console.error("Error loading appointments:", error);
      Alert.alert("Greška", "Nije moguće učitati zakazivanja");
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const onRefresh = () => {
    setIsRefreshing(true);
    loadAppointments();
  };

  const updateAppointmentStatus = async (appointmentId, newStatus) => {
    try {
      setIsLoading(true);
      await ApiService.updateBookingStatus(appointmentId, newStatus);
      Alert.alert("Uspeh", "Status zakazivanja je ažuriran");
      await loadAppointments(); // Reload appointments
    } catch (error) {
      console.error("Error updating appointment status:", error);
      Alert.alert("Greška", "Nije moguće ažurirati status zakazivanja");
    } finally {
      setIsLoading(false);
    }
  };

  const deleteAppointment = async (appointmentId) => {
    try {
      setIsLoading(true);
      await ApiService.deleteBooking(appointmentId);
      Alert.alert("Uspeh", "Zakazivanje je uspešno obrisano");
      await loadAppointments(); // Reload appointments
    } catch (error) {
      console.error("Error deleting appointment:", error);
      Alert.alert("Greška", "Nije moguće obrisati zakazivanje");
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusChange = (appointment, newStatus) => {
    const statusText = getStatusText(newStatus);
    Alert.alert(
      "Promena statusa",
      `Da li želite da promenite status zakazivanja na "${statusText}"?`,
      [
        { text: "Otkaži", style: "cancel" },
        {
          text: "Potvrdi",
          onPress: () => updateAppointmentStatus(appointment._id, newStatus),
        },
      ]
    );
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("sr-RS", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "confirmed":
        return "#10b981";
      case "pending":
        return "#f59e0b";
      case "cancelled":
        return "#ef4444";
      case "completed":
        return "#6b7280";
      default:
        return "#6b7280";
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case "confirmed":
        return "Potvrđeno";
      case "pending":
        return "Na čekanju";
      case "cancelled":
        return "Otkazano";
      case "completed":
        return "Završeno";
      default:
        return status;
    }
  };

  // Helper function to check if appointment is today
  const isToday = (dateString) => {
    const today = new Date();
    const appointmentDate = new Date(dateString);
    return (
      today.getFullYear() === appointmentDate.getFullYear() &&
      today.getMonth() === appointmentDate.getMonth() &&
      today.getDate() === appointmentDate.getDate()
    );
  };

  // Helper function to check if appointment is in the past
  const isPast = (dateString) => {
    const today = new Date();
    const appointmentDate = new Date(dateString);

    // Set today to start of day for accurate comparison
    today.setHours(0, 0, 0, 0);
    appointmentDate.setHours(0, 0, 0, 0);

    return appointmentDate < today;
  };

  // Filter and sort appointments
  const filteredAppointments = appointments
    .filter((appointment) => {
      if (filter === "current") {
        // Show only current and future appointments (not past)
        return !isPast(appointment.date);
      }
      if (filter === "past") {
        // Show only past appointments
        return isPast(appointment.date);
      }
      if (filter === "confirmed") {
        // Show both confirmed and completed appointments
        return (
          appointment.status === "confirmed" ||
          appointment.status === "completed"
        );
      }
      // For other status filters (pending, cancelled)
      return appointment.status === filter;
    })
    .sort((a, b) => {
      // Sort by date (closest to today first)
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return dateA - dateB;
    });

  const filterButtons = [
    { key: "current", label: "Trenutni" },
    { key: "pending", label: "Na čekanju" },
    { key: "confirmed", label: "Potvrđeni" },
    { key: "cancelled", label: "Otkazani" },
    { key: "past", label: "Prošli" },
  ];

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2563eb" />
        <Text style={styles.loadingText}>Učitavanje zakazivanja...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Zakazivanja</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowForm(true)}
        >
          <Text style={styles.addButtonText}>+ Dodaj termin</Text>
        </TouchableOpacity>
      </View>

      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        <View style={styles.filterGrid}>
          {filterButtons.map((button) => (
            <TouchableOpacity
              key={button.key}
              style={[
                styles.filterButton,
                filter === button.key && styles.activeFilterButton,
              ]}
              onPress={() => setFilter(button.key)}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  filter === button.key && styles.activeFilterButtonText,
                ]}
              >
                {button.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <ScrollView
        style={styles.appointmentsList}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
        }
      >
        {filteredAppointments.length > 0 ? (
          filteredAppointments.map((appointment, index) => (
            <View
              key={appointment._id || index}
              style={[
                styles.appointmentCard,
                isToday(appointment.date) && styles.todayAppointmentCard,
              ]}
            >
              {/* Today indicator */}
              {isToday(appointment.date) && (
                <View style={styles.todayIndicator}>
                  <Text style={styles.todayIndicatorText}>🗓️ DANAS</Text>
                </View>
              )}

              <View style={styles.appointmentHeader}>
                <Text style={styles.clientName}>{appointment.clientName}</Text>
                <View
                  style={[
                    styles.statusBadge,
                    { backgroundColor: getStatusColor(appointment.status) },
                  ]}
                >
                  <Text style={styles.statusText}>
                    {getStatusText(appointment.status)}
                  </Text>
                </View>
              </View>

              <Text style={styles.serviceName}>{appointment.serviceName}</Text>
              <Text style={styles.appointmentDateTime}>
                {formatDate(appointment.date)} u {formatTime(appointment.date)}
              </Text>
              <Text style={styles.clientPhone}>{appointment.clientPhone}</Text>

              {/* Action Buttons */}
              {appointment.status === "pending" && (
                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    style={[styles.actionButton, styles.confirmButton]}
                    onPress={() => handleStatusChange(appointment, "confirmed")}
                  >
                    <Text style={styles.actionButtonText}>Potvrdi</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.actionButton, styles.cancelButton]}
                    onPress={() => handleStatusChange(appointment, "cancelled")}
                  >
                    <Text style={styles.actionButtonText}>Otkaži</Text>
                  </TouchableOpacity>
                </View>
              )}

              {appointment.status === "confirmed" && (
                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    style={[styles.actionButton, styles.cancelButton]}
                    onPress={() => handleStatusChange(appointment, "cancelled")}
                  >
                    <Text style={styles.actionButtonText}>Otkaži</Text>
                  </TouchableOpacity>
                </View>
              )}

              {/* Delete button for all appointments */}
              <View style={[styles.actionButtons, { marginTop: 8 }]}>
                <TouchableOpacity
                  style={[styles.actionButton, styles.deleteButton]}
                  onPress={() => {
                    Alert.alert(
                      "Brisanje zakazivanja",
                      `Da li ste sigurni da želite da obrišete zakazivanje za ${appointment.clientName}?`,
                      [
                        { text: "Otkaži", style: "cancel" },
                        {
                          text: "Obriši",
                          style: "destructive",
                          onPress: () => deleteAppointment(appointment._id),
                        },
                      ]
                    );
                  }}
                >
                  <Text style={styles.actionButtonText}>🗑️ Obriši</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateTitle}>
              {filter === "current"
                ? "Nema trenutnih zakazivanja"
                : filter === "past"
                ? "Nema prošlih zakazivanja"
                : `Nema ${filterButtons
                    .find((b) => b.key === filter)
                    ?.label.toLowerCase()} zakazivanja`}
            </Text>
            <Text style={styles.emptyStateText}>
              Zakazivanja će se pojaviti ovde kada ih klijenti naprave
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Modal for adding new appointment */}
      <Modal
        visible={showForm}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowForm(false)}
      >
        <KeyboardAvoidingView
          style={styles.modalContainer}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => {
                resetForm();
                setShowForm(false);
              }}
            >
              <Text style={styles.cancelButtonText}>Otkaži</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Dodaj novi termin</Text>
            <TouchableOpacity
              style={[styles.saveButton, isSaving && styles.disabledButton]}
              onPress={handleSubmitBooking}
              disabled={isSaving}
            >
              <Text style={styles.saveButtonText}>
                {isSaving ? "Čuva..." : "Sačuvaj"}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.formContainer}>
            {/* Name Input */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Ime klijenta</Text>
              <TextInput
                style={styles.input}
                value={formData.name}
                onChangeText={(value) => handleFormChange("name", value)}
                placeholder="Unesite ime klijenta"
                placeholderTextColor="#9ca3af"
              />
            </View>

            {/* Phone Input */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Telefon</Text>
              <TextInput
                style={styles.input}
                value={formData.phone}
                onChangeText={(value) => handleFormChange("phone", value)}
                placeholder="Npr. 0601234567"
                placeholderTextColor="#9ca3af"
                keyboardType="phone-pad"
              />
            </View>

            {/* Service Picker */}
            {services.length > 0 && (
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Usluga</Text>
                <View style={styles.pickerContainer}>
                  {services.map((service) => (
                    <TouchableOpacity
                      key={service._id}
                      style={[
                        styles.pickerOption,
                        formData.serviceId === service._id &&
                          styles.selectedOption,
                      ]}
                      onPress={() => handleFormChange("serviceId", service._id)}
                    >
                      <Text
                        style={[
                          styles.pickerOptionText,
                          formData.serviceId === service._id &&
                            styles.selectedOptionText,
                        ]}
                      >
                        {service.name} - {service.durationMins} min
                        {service.price && ` (${service.price} RSD)`}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}

            {/* Date Picker */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Datum</Text>
              <TouchableOpacity
                style={styles.datePickerButton}
                onPress={() => setShowDatePicker(true)}
              >
                <Text
                  style={[
                    styles.datePickerText,
                    !formData.date && styles.placeholderText,
                  ]}
                >
                  {formatDisplayDate(formData.date)}
                </Text>
                <Text style={styles.datePickerIcon}>📅</Text>
              </TouchableOpacity>
            </View>

            {/* Time Picker */}
            {availableTimeSlots.length > 0 && (
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Vreme</Text>
                <View style={styles.timeSlotContainer}>
                  {availableTimeSlots.map((time) => (
                    <TouchableOpacity
                      key={time}
                      style={[
                        styles.timeSlot,
                        formData.time === time && styles.selectedTimeSlot,
                      ]}
                      onPress={() => handleFormChange("time", time)}
                    >
                      <Text
                        style={[
                          styles.timeSlotText,
                          formData.time === time && styles.selectedTimeSlotText,
                        ]}
                      >
                        {time}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}

            {formData.date && availableTimeSlots.length === 0 && (
              <View style={styles.inputGroup}>
                <Text style={styles.errorText}>
                  Nema dostupnih termina za izabrani datum
                </Text>
              </View>
            )}
          </ScrollView>
        </KeyboardAvoidingView>

        {/* Date Picker Modal */}
        {showDatePicker && (
          <DateTimePicker
            value={selectedDate}
            mode="date"
            display={Platform.OS === "ios" ? "spinner" : "default"}
            onChange={handleDateChange}
            minimumDate={new Date()} // Can't select past dates
            maximumDate={new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)} // Max 1 year ahead
          />
        )}
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
    paddingTop: Platform.OS === "android" ? StatusBar.currentHeight : 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f8f9fa",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#6b7280",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    backgroundColor: "#ffffff",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1f2937",
  },
  addButton: {
    backgroundColor: "#2563eb",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addButtonText: {
    color: "#ffffff",
    fontWeight: "600",
    fontSize: 14,
  },
  filterContainer: {
    backgroundColor: "#ffffff",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
    height: 120, // Povećana visina za 2 reda dugmića
  },
  filterGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-around",
    alignItems: "center",
    height: 80, // Povećana visina za 2 reda
  },
  filterButton: {
    backgroundColor: "#ffffff",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e5e7eb",
    minWidth: "30%", // Širi dugmići za bolji tekst
    maxWidth: "48%", // Maksimalno 2 dugmeta u redu
    alignItems: "center",
    justifyContent: "center",
    height: 36, // Fiksna visina dugmeta
    marginBottom: 8, // Razmak između redova
  },
  activeFilterButton: {
    backgroundColor: "#2563eb",
    borderColor: "#2563eb",
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#374151",
  },
  activeFilterButtonText: {
    color: "#ffffff",
  },
  appointmentsList: {
    padding: 20,
  },
  appointmentCard: {
    backgroundColor: "#ffffff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  todayAppointmentCard: {
    backgroundColor: "#fef3c7",
    borderWidth: 2,
    borderColor: "#f59e0b",
  },
  todayIndicator: {
    backgroundColor: "#f59e0b",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: "flex-start",
    marginBottom: 8,
  },
  todayIndicatorText: {
    color: "#ffffff",
    fontSize: 12,
    fontWeight: "bold",
  },
  appointmentHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  clientName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1f2937",
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: "#ffffff",
    fontSize: 12,
    fontWeight: "600",
  },
  serviceName: {
    fontSize: 14,
    color: "#6b7280",
    marginBottom: 4,
  },
  appointmentDateTime: {
    fontSize: 14,
    color: "#6b7280",
    marginBottom: 4,
  },
  clientPhone: {
    fontSize: 14,
    color: "#6b7280",
    marginBottom: 12,
  },
  actionButtons: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 6,
    alignItems: "center",
    backgroundColor: "#992E2EFF",
  },
  confirmButton: {
    backgroundColor: "#10b981",
  },
  cancelButton: {
    backgroundColor: "#ef4444",
  },
  deleteButton: {
    backgroundColor: "#dc2626",
  },
  actionButtonText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
    backgroundColor: "#ffffff",
    borderRadius: 12,
    marginTop: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: "#6b7280",
    textAlign: "center",
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    backgroundColor: "#ffffff",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1f2937",
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  cancelButtonText: {
    color: "#ef4444",
    fontWeight: "600",
  },
  saveButton: {
    backgroundColor: "#2563eb",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  disabledButton: {
    opacity: 0.5,
  },
  formContainer: {
    flex: 1,
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: "#d1d5db",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: "#ffffff",
    color: "#1f2937",
  },
  helperText: {
    fontSize: 12,
    color: "#6b7280",
    marginTop: 4,
  },
  errorText: {
    fontSize: 14,
    color: "#ef4444",
    textAlign: "center",
    padding: 12,
    backgroundColor: "#fef2f2",
    borderRadius: 8,
  },
  pickerContainer: {
    backgroundColor: "#ffffff",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#d1d5db",
  },
  pickerOption: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#f3f4f6",
  },
  selectedOption: {
    backgroundColor: "#dbeafe",
  },
  pickerOptionText: {
    fontSize: 14,
    color: "#1f2937",
  },
  selectedOptionText: {
    color: "#2563eb",
    fontWeight: "600",
  },
  timeSlotContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  timeSlot: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: "#f3f4f6",
    borderWidth: 1,
    borderColor: "#d1d5db",
  },
  selectedTimeSlot: {
    backgroundColor: "#2563eb",
    borderColor: "#2563eb",
  },
  timeSlotText: {
    fontSize: 14,
    color: "#1f2937",
  },
  selectedTimeSlotText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  datePickerButton: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#d1d5db",
    borderRadius: 8,
    padding: 12,
    backgroundColor: "#ffffff",
  },
  datePickerText: {
    fontSize: 16,
    color: "#1f2937",
  },
  placeholderText: {
    color: "#9ca3af",
  },
  datePickerIcon: {
    fontSize: 18,
  },
});

export default AppointmentsScreen;
