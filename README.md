# Z<PERSON>zi AI Mobile App

A React Native mobile application for salon owners that provides intelligent call-routing assistance and includes all web app functionality.

## Features

### Core Web App Features (Mobile Optimized)
- **User Authentication** - Login/Register with existing backend
- **Dashboard** - Overview of bookings, analytics, and quick actions
- **Profile Management** - Edit business information and user details
- **Working Hours** - Configure salon operating hours
- **Services Management** - Add, edit, and delete salon services
- **Appointments** - View and manage bookings with status updates
- **AI Assistant** - Chat interface for booking assistance

### Mobile-Specific Features
- **Call Routing System** - Automatically handle incoming calls based on business hours
- **SMS Automation** - Send booking links via SMS when calls are handled
- **Three Call Handling Modes**:
  1. **Always Allow + SMS** - Let calls ring, then send SMS
  2. **Business Hours Only** - Ring during hours, reject + SMS outside hours
  3. **Always Reject + SMS** - Immediately reject all calls and send SMS
- **Background Service** - Persistent call monitoring
- **Local Settings Storage** - Offline configuration management
- **Permission Management** - Handle Android call and SMS permissions

## Technology Stack

- **React Native** with Expo
- **React Navigation** for navigation
- **AsyncStorage** for local data persistence
- **Existing Backend Integration** (Next.js + MongoDB)

## Project Structure

```
ZakaziAIMobile/
├── src/
│   ├── components/          # Reusable UI components
│   ├── screens/            # Screen components
│   │   ├── LoginScreen.js
│   │   ├── RegisterScreen.js
│   │   ├── DashboardScreen.js
│   │   ├── ProfileScreen.js
│   │   ├── WorkingHoursScreen.js
│   │   ├── ServicesScreen.js
│   │   ├── AppointmentsScreen.js
│   │   ├── AIAssistantScreen.js
│   │   └── MobileSettingsScreen.js
│   ├── navigation/         # Navigation configuration
│   │   └── AppNavigator.js
│   ├── context/           # React Context providers
│   │   └── AuthContext.js
│   └── services/          # API services
│       └── api.js
├── App.js                 # Main app component
└── package.json
```

## Setup Instructions

### Prerequisites
- Node.js (v16 or higher)
- Expo CLI (`npm install -g @expo/cli`)
- Android Studio (for Android development)
- Expo Go app on your phone (for testing)

### Installation

1. **Navigate to the mobile app directory:**
   ```bash
   cd ZakaziAIMobile
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Update backend URL:**
   Edit `src/services/api.js` and update the `BASE_URL` to point to your backend:
   ```javascript
   const BASE_URL = 'https://your-backend-url.com'; // Replace with your actual URL
   ```

4. **Start the development server:**
   ```bash
   npm start
   ```

5. **Run on device:**
   - Scan the QR code with Expo Go app (Android/iOS)
   - Or press 'a' to open Android emulator
   - Or press 'w' to open in web browser

## Configuration

### Backend Integration
The mobile app connects to your existing Zakazi AI backend. Make sure to:

1. Update the API base URL in `src/services/api.js`
2. Ensure your backend accepts requests from the mobile app
3. Configure CORS if needed

### Mobile Settings
The app stores mobile-specific settings locally using AsyncStorage:
- Call handling preferences
- SMS templates
- Service status
- Permission states

## Call Routing System

### How It Works
1. **Background Service** monitors incoming calls
2. **Time Check** compares current time with saved working hours
3. **Action Decision** based on selected call handling mode:
   - Allow call to ring or reject immediately
   - Send SMS with booking link via device's SIM card
4. **Local Storage** keeps settings available offline

### Required Permissions
- `READ_PHONE_STATE` - Monitor incoming calls
- `CALL_PHONE` - Manage call actions
- `ANSWER_PHONE_CALLS` - Handle call routing
- `SEND_SMS` - Send booking link messages
- `RECEIVE_BOOT_COMPLETED` - Start service on device boot
- `FOREGROUND_SERVICE` - Run background service
- `WAKE_LOCK` - Keep service active

## Development Status

### ✅ Completed Features
- Project setup with Expo and React Native
- Authentication system (login/register)
- Main navigation with bottom tabs
- Dashboard with booking statistics
- Profile management
- Working hours configuration
- Services management
- Appointments management with status updates
- AI Assistant chat interface
- Mobile settings screen for call routing
- Local storage for settings

### 🚧 In Progress
- Call interception service implementation
- SMS automation system
- Permission handling
- Background service management

### 📋 Planned Features
- Call logs and analytics
- Booking link generation
- App onboarding flow
- Testing and QA
- App store deployment

## API Endpoints Used

The mobile app uses the following existing backend endpoints:
- `POST /api/login` - User authentication
- `POST /api/register` - User registration
- `GET /api/me` - Get current user data
- `POST /api/update-user` - Update user profile
- `GET /api/working-hours` - Get working hours
- `POST /api/update-schedule` - Update working hours
- `GET /api/services` - Get salon services
- `POST /api/services/create` - Create new service
- `POST /api/services/update` - Update service
- `POST /api/services/delete` - Delete service
- `GET /api/bookings` - Get appointments
- `POST /api/bookings/update-status` - Update appointment status
- `POST /api/ai/booking-assistant` - AI chat interface

## Testing

To test the mobile app:

1. **Authentication**: Test login/register with existing accounts
2. **Data Sync**: Verify data matches between web and mobile
3. **Offline Mode**: Test settings storage when offline
4. **Call Simulation**: Test call handling logic (requires device)
5. **SMS Testing**: Verify SMS sending (requires device with SIM)

## Deployment

### Android APK Build
```bash
expo build:android
```

### iOS Build (requires Apple Developer account)
```bash
expo build:ios
```

### App Store Submission
1. Build production APK/IPA
2. Test thoroughly on physical devices
3. Prepare store listings and screenshots
4. Submit to Google Play Store / Apple App Store

## Support

For issues or questions:
1. Check the existing backend API documentation
2. Review React Native and Expo documentation
3. Test on physical Android device for call/SMS features

## License

This mobile app is part of the Zakazi AI project.
