import AsyncStorage from "@react-native-async-storage/async-storage";
import { Linking } from "react-native";

// Headless JS task za pozadinsko rukovanje pozivima
const CallDetectionHeadlessTask = async (taskData) => {
  console.log("🎯 Headless JS task started with data:", taskData);

  try {
    const { event, phoneNumber } = taskData;

    // Učitaj podešavanja iz AsyncStorage
    const savedSettings = await AsyncStorage.getItem("mobileSettings");
    if (!savedSettings) {
      console.log("⏭️ No settings found, skipping");
      return;
    }

    const settings = JSON.parse(savedSettings);
    console.log("⚙️ Loaded settings in headless task:", settings);

    // Proveri da li je servis omogućen
    if (!settings.serviceEnabled) {
      console.log("⏭️ Service disabled, skipping");
      return;
    }

    // Rukuj različitim tipovima događaja
    switch (event) {
      case "Missed":
        await handleMissedCall(phoneNumber, settings);
        break;
      case "Incoming":
        await handleIncomingCall(phoneNumber, settings);
        break;
      case "Offhook":
        console.log("📞 Call answered in background");
        break;
      case "Disconnected":
        console.log("📴 Call ended in background");
        break;
      default:
        console.log("❓ Unknown call event in background:", event);
    }

    console.log("✅ Headless task completed successfully");
  } catch (error) {
    console.error("❌ Error in headless task:", error);
  }
};

const handleMissedCall = async (phoneNumber, settings) => {
  try {
    console.log("📵 MISSED CALL in background from:", phoneNumber);

    if (!settings.smsEnabled || !settings.smsTemplate) {
      console.log("⏭️ SMS disabled or no template, skipping");
      return;
    }

    // Zameni placeholders u template-u
    let message = settings.smsTemplate;
    
    // Učitaj user data za business name
    const userData = await AsyncStorage.getItem("userData");
    if (userData) {
      const parsedUserData = JSON.parse(userData);
      message = message.replace(/{businessName}/g, parsedUserData.businessName || "naša firma");
      
      // Generiši booking link
      const bookingLink = parsedUserData.slug 
        ? `https://zakaziai.com/book/${parsedUserData.slug}`
        : "https://zakaziai.com";
      message = message.replace(/{bookingLink}/g, bookingLink);
    }

    console.log("📱 Sending SMS in background to:", phoneNumber);
    console.log("💬 Message:", message);

    // Pošalji SMS preko Linking API
    await sendSMSViaLinking(phoneNumber, message);

    console.log("✅ SMS sent successfully in background");
  } catch (error) {
    console.error("❌ Error handling missed call in background:", error);
  }
};

const handleIncomingCall = async (phoneNumber, settings) => {
  try {
    console.log("📲 INCOMING CALL in background from:", phoneNumber);

    const shouldHandle = shouldHandleCall(settings);
    console.log("🤔 Should handle call in background:", shouldHandle);

    if (shouldHandle) {
      console.log("🚫 HANDLING INCOMING CALL in background - will send SMS when missed");
      // SMS će biti poslat kada poziv postane propušten
    } else {
      console.log("✅ ALLOWING CALL to ring normally in background");
    }
  } catch (error) {
    console.error("❌ Error handling incoming call in background:", error);
  }
};

const shouldHandleCall = (settings) => {
  if (!settings.serviceEnabled) {
    return false;
  }

  switch (settings.callHandlingMode) {
    case "always_allow":
      return false;
    case "always_reject":
      return true;
    case "business_hours_only":
      return !isInWorkingHours();
    default:
      return false;
  }
};

const isInWorkingHours = () => {
  const now = new Date();
  const hour = now.getHours();
  const day = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

  // Simple check: 9 AM to 5 PM, Monday to Friday
  const isWeekday = day >= 1 && day <= 5;
  const isWorkingHour = hour >= 9 && hour < 17;

  return isWeekday && isWorkingHour;
};

const sendSMSViaLinking = async (phoneNumber, message) => {
  try {
    // Koristi Linking API za otvaranje SMS aplikacije
    const url = `sms:${phoneNumber}?body=${encodeURIComponent(message)}`;
    
    // Proveri da li je moguće otvoriti SMS aplikaciju
    const canOpen = await Linking.canOpenURL(url);
    if (canOpen) {
      await Linking.openURL(url);
      console.log("📱 SMS app opened with pre-filled message");
    } else {
      console.log("❌ Cannot open SMS app");
    }
  } catch (error) {
    console.error("❌ Error opening SMS app:", error);
    throw error;
  }
};

export default CallDetectionHeadlessTask;
