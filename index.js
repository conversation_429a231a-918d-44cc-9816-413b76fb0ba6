import { registerRootComponent } from "expo";
import { AppRegistry } from "react-native";

import App from "./App";
import CallDetectionHeadlessTask from "./src/services/CallDetectionHeadlessTask";

// Registruj Headless JS task za pozadinsko rukovanje pozivima
AppRegistry.registerHeadlessTask(
  "CallDetectionHeadlessTask",
  () => CallDetectionHeadlessTask
);

// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);
