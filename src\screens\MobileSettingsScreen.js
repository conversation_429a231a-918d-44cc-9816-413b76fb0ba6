import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator,
  StatusBar,
  Platform,
  TextInput,
  Modal,
  PermissionsAndroid,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import CallInterceptorService from "../services/CallInterceptorService";

const MobileSettingsScreen = () => {
  const [settings, setSettings] = useState({
    callHandlingEnabled: false,
    callHandlingMode: "business_hours_only",
    smsEnabled: true,
    smsTemplate:
      "Zdravo! Hvala što ste pozvali {businessName}. Možete zakazati termin ovde: {bookingLink}",
    serviceEnabled: false,
    permissionsGranted: {
      phone: false,
      sms: false,
      callLog: false,
      phoneNumbers: false,
      answerCalls: false,
    },
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showSmsEditor, setShowSmsEditor] = useState(false);
  const [tempSmsTemplate, setTempSmsTemplate] = useState("");
  const [userData, setUserData] = useState(null);

  const callHandlingModes = [
    {
      key: "always_allow",
      title: "Uvek dozvoli pozive + SMS",
      description:
        "Pozivi će uvek zvoniti, SMS će biti poslat samo za propuštene pozive",
    },
    {
      key: "business_hours_only",
      title: "Pozivi samo u radno vreme",
      description:
        "Pozivi će zvoniti tokom radnog vremena, van radnog vremena SMS za propuštene pozive",
    },
    {
      key: "always_reject",
      title: "SMS za sve propuštene pozive",
      description:
        "SMS će biti poslat za sve propuštene pozive bez obzira na vreme",
    },
  ];

  useEffect(() => {
    loadSettings();
    return () => {
      // Cleanup when component unmounts
      if (settings.serviceEnabled) {
        CallInterceptorService.cleanup();
      }
    };
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);

      // Load user data
      const storedUserData = await AsyncStorage.getItem("userData");
      if (storedUserData) {
        const parsedUserData = JSON.parse(storedUserData);
        setUserData(parsedUserData);
      }

      // Load mobile settings
      const savedSettings = await AsyncStorage.getItem("mobileSettings");
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(parsedSettings);
        setTempSmsTemplate(parsedSettings.smsTemplate);
      } else {
        const defaultTemplate = storedUserData
          ? `Zdravo! Hvala što ste pozvali ${
              JSON.parse(storedUserData).businessName || "{businessName}"
            }. Možete zakazati termin ovde: {bookingLink}`
          : "Zdravo! Hvala što ste pozvali {businessName}. Možete zakazati termin ovde: {bookingLink}";

        setSettings((prev) => ({ ...prev, smsTemplate: defaultTemplate }));
        setTempSmsTemplate(defaultTemplate);
      }

      // Check permissions status
      await checkPermissions();
    } catch (error) {
      console.error("Error loading settings:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async (newSettings) => {
    try {
      setIsSaving(true);
      await AsyncStorage.setItem("mobileSettings", JSON.stringify(newSettings));
      await CallInterceptorService.saveSettings(newSettings);
      setSettings(newSettings);
      Alert.alert("Uspeh", "Podešavanja su sačuvana");
    } catch (error) {
      console.error("Error saving settings:", error);
      Alert.alert("Greška", "Nije moguće sačuvati podešavanja");
    } finally {
      setIsSaving(false);
    }
  };

  const checkPermissions = async () => {
    try {
      if (Platform.OS === "android") {
        const phonePermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE
        );
        const smsPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.SEND_SMS
        );
        const callLogPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.READ_CALL_LOG
        );

        // Dodatne dozvole za react-native-call-detection
        let phoneNumbersPermission = true;
        let answerCallsPermission = true;

        try {
          phoneNumbersPermission = await PermissionsAndroid.check(
            "android.permission.READ_PHONE_NUMBERS"
          );
        } catch (e) {
          console.log(
            "READ_PHONE_NUMBERS permission not available on this device"
          );
        }

        try {
          answerCallsPermission = await PermissionsAndroid.check(
            "android.permission.ANSWER_PHONE_CALLS"
          );
        } catch (e) {
          console.log(
            "ANSWER_PHONE_CALLS permission not available on this device"
          );
        }

        const permissionsGranted = {
          phone: phonePermission,
          sms: smsPermission,
          callLog: callLogPermission,
          phoneNumbers: phoneNumbersPermission,
          answerCalls: answerCallsPermission,
        };

        setSettings((prev) => ({
          ...prev,
          permissionsGranted,
        }));

        console.log("📋 Permissions status:", permissionsGranted);
      }
    } catch (error) {
      console.error("Error checking permissions:", error);
    }
  };

  const requestPermissions = async () => {
    try {
      console.log("🔐 Requesting permissions...");

      if (Platform.OS === "android") {
        const permissions = [
          PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE,
          PermissionsAndroid.PERMISSIONS.SEND_SMS,
          PermissionsAndroid.PERMISSIONS.READ_CALL_LOG,
        ];

        // Dodaj dodatne dozvole ako su dostupne
        try {
          permissions.push("android.permission.READ_PHONE_NUMBERS");
        } catch (e) {
          console.log("READ_PHONE_NUMBERS not available");
        }

        try {
          permissions.push("android.permission.ANSWER_PHONE_CALLS");
        } catch (e) {
          console.log("ANSWER_PHONE_CALLS not available");
        }

        console.log("📝 Requesting permissions:", permissions);
        const granted = await PermissionsAndroid.requestMultiple(permissions);
        console.log("✅ Permission results:", granted);

        const permissionsGranted = {
          phone:
            granted[PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE] ===
            PermissionsAndroid.RESULTS.GRANTED,
          sms:
            granted[PermissionsAndroid.PERMISSIONS.SEND_SMS] ===
            PermissionsAndroid.RESULTS.GRANTED,
          callLog:
            granted[PermissionsAndroid.PERMISSIONS.READ_CALL_LOG] ===
            PermissionsAndroid.RESULTS.GRANTED,
          phoneNumbers:
            granted["android.permission.READ_PHONE_NUMBERS"] ===
              PermissionsAndroid.RESULTS.GRANTED || true, // fallback to true if not available
          answerCalls:
            granted["android.permission.ANSWER_PHONE_CALLS"] ===
              PermissionsAndroid.RESULTS.GRANTED || true, // fallback to true if not available
        };

        setSettings((prev) => ({
          ...prev,
          permissionsGranted,
        }));

        const allGranted = Object.values(permissionsGranted).every(
          (permission) => permission
        );

        if (allGranted) {
          Alert.alert(
            "Uspeh",
            "Sve dozvole su odobrene! Možete sada da pokrenete servis."
          );
        } else {
          const deniedPermissions = Object.entries(permissionsGranted)
            .filter(([, value]) => !value)
            .map(([key]) => {
              switch (key) {
                case "phone":
                  return "Čitanje stanja telefona";
                case "sms":
                  return "Slanje SMS poruka";
                case "callLog":
                  return "Pristup istoriji poziva";
                case "phoneNumbers":
                  return "Čitanje brojeva telefona";
                case "answerCalls":
                  return "Upravljanje pozivima";
                default:
                  return key;
              }
            });

          Alert.alert(
            "Potrebne dodatne dozvole",
            `Sledeće dozvole nisu odobrene:\n• ${deniedPermissions.join(
              "\n• "
            )}\n\nBez ovih dozvola aplikacija neće moći da radi kako treba.`,
            [
              { text: "OK" },
              {
                text: "Pokušaj ponovo",
                onPress: () => requestPermissions(),
              },
            ]
          );
        }
      } else {
        Alert.alert("iOS podrška", "iOS verzija će biti dostupna uskoro.");
      }
    } catch (error) {
      console.error("Error requesting permissions:", error);
      Alert.alert(
        "Greška",
        `Došlo je do greške pri zahtevanju dozvola: ${error.message}`
      );
    }
  };

  const toggleCallHandling = (enabled) => {
    if (enabled && !settings.permissionsGranted.phone) {
      Alert.alert(
        "Dozvole potrebne",
        "Da biste omogućili detekciju poziva, potrebne su dozvole za čitanje stanja telefona.",
        [
          { text: "Otkaži", style: "cancel" },
          { text: "Zatraži dozvole", onPress: requestPermissions },
        ]
      );
      return;
    }

    const newSettings = {
      ...settings,
      callHandlingEnabled: enabled,
    };
    saveSettings(newSettings);
  };

  const selectCallHandlingMode = (mode) => {
    const newSettings = {
      ...settings,
      callHandlingMode: mode,
    };
    saveSettings(newSettings);
  };

  const toggleSMS = (enabled) => {
    if (enabled && !settings.permissionsGranted.sms) {
      Alert.alert(
        "Dozvole potrebne",
        "Da biste omogućili slanje SMS poruka, potrebne su dozvole za pristup SMS funkcijama.",
        [
          { text: "Otkaži", style: "cancel" },
          { text: "Zatraži dozvole", onPress: requestPermissions },
        ]
      );
      return;
    }

    const newSettings = {
      ...settings,
      smsEnabled: enabled,
    };
    saveSettings(newSettings);
  };

  const toggleService = async (enabled) => {
    if (
      enabled &&
      (!settings.permissionsGranted.phone || !settings.permissionsGranted.sms)
    ) {
      Alert.alert(
        "Dozvole potrebne",
        "Da biste pokrenuli servis za detekciju poziva, potrebne su osnovne dozvole (čitanje stanja telefona i slanje SMS-a).",
        [
          { text: "Otkaži", style: "cancel" },
          { text: "Zatraži dozvole", onPress: requestPermissions },
        ]
      );
      return;
    }

    try {
      setIsSaving(true);

      const newSettings = {
        ...settings,
        serviceEnabled: enabled,
      };

      if (enabled) {
        console.log("🚀 Starting call detection service...");
        await CallInterceptorService.startService();
        Alert.alert(
          "Servis pokrenut",
          "Detekcija poziva je aktivna - SMS će biti poslat za propuštene pozive"
        );
      } else {
        console.log("🛑 Stopping call detection service...");
        await CallInterceptorService.stopService();
        Alert.alert("Servis zaustavljen", "Detekcija poziva je zaustavljena");
      }

      await saveSettings(newSettings);
    } catch (error) {
      console.error("❌ Error toggling service:", error);
      Alert.alert("Greška", `Došlo je do greške: ${error.message}`);

      // Revert settings on error
      const revertedSettings = {
        ...settings,
        serviceEnabled: !enabled,
      };
      setSettings(revertedSettings);
    } finally {
      setIsSaving(false);
    }
  };

  const openSmsEditor = () => {
    setTempSmsTemplate(settings.smsTemplate);
    setShowSmsEditor(true);
  };

  const saveSmsTemplate = () => {
    const newSettings = {
      ...settings,
      smsTemplate: tempSmsTemplate,
    };
    saveSettings(newSettings);
    setShowSmsEditor(false);
  };

  const resetSmsTemplate = () => {
    const defaultTemplate = userData
      ? `Zdravo! Hvala što ste pozvali ${
          userData.businessName || "{businessName}"
        }. Možete zakazati termin ovde: {bookingLink}`
      : "Zdravo! Hvala što ste pozvali {businessName}. Možete zakazati termin ovde: {bookingLink}";

    setTempSmsTemplate(defaultTemplate);
  };

  const insertPlaceholder = (placeholder) => {
    setTempSmsTemplate((prev) => prev + placeholder);
  };

  const generateBookingLink = () => {
    if (userData && userData.slug) {
      return `https://zakaziai.com/book/${userData.slug}`;
    }
    return "{bookingLink}";
  };

  const previewSmsMessage = () => {
    let preview = tempSmsTemplate;

    if (userData) {
      preview = preview.replace(
        /{businessName}/g,
        userData.businessName || "Vaš salon"
      );
      preview = preview.replace(/{bookingLink}/g, generateBookingLink());
    }

    Alert.alert("Pregled SMS poruke", preview);
  };

  const testSmsFunction = () => {
    Alert.alert(
      "Test SMS",
      "Unesite broj telefona za test SMS (format: +381601234567):",
      [
        { text: "Otkaži", style: "cancel" },
        {
          text: "Test",
          onPress: () => {
            const testNumber = "+381601234567";
            testSMS(testNumber);
          },
        },
      ]
    );
  };

  const testSMS = async (phoneNumber) => {
    try {
      setIsSaving(true);

      let message = settings.smsTemplate;
      if (userData) {
        message = message.replace(
          /{businessName}/g,
          userData.businessName || "Vaš salon"
        );
        message = message.replace(/{bookingLink}/g, generateBookingLink());
      }

      console.log("🧪 Testing SMS to:", phoneNumber);
      await CallInterceptorService.testSMS(phoneNumber, message);
      Alert.alert("Uspeh", "Test SMS je poslat!");
    } catch (error) {
      console.error("❌ Error testing SMS:", error);
      Alert.alert("Greška", `Greška pri slanju test SMS-a: ${error.message}`);
    } finally {
      setIsSaving(false);
    }
  };

  const simulatePermissions = () => {
    Alert.alert(
      "Simulacija dozvola",
      "Ovo je test funkcija za development. Želite li da simulirate odobrene dozvole?",
      [
        { text: "Ne", style: "cancel" },
        {
          text: "Da",
          onPress: () => {
            const permissionsGranted = {
              phone: true,
              sms: true,
              callLog: true,
              phoneNumbers: true,
              answerCalls: true,
            };

            setSettings((prev) => ({
              ...prev,
              permissionsGranted,
            }));

            Alert.alert("Simulacija", "Dozvole su simulirane kao odobrene!");
          },
        },
      ]
    );
  };

  const testCallDetection = async () => {
    try {
      console.log("🧪 TESTING CALL DETECTION SYSTEM");

      const serviceStatus = CallInterceptorService.getServiceStatus();
      console.log("📊 Service running:", serviceStatus);

      if (!serviceStatus) {
        Alert.alert(
          "Test info",
          "Servis nije pokrenut. Pokrenite servis pa testirajte pozivom."
        );
        return;
      }

      Alert.alert(
        "Test detekcije poziva",
        "Servis je aktivan. Pozovite ovaj telefon sa drugog broja da testirate detekciju poziva i SMS funkcionalnost.\n\nPratite console log za detalje."
      );
    } catch (error) {
      console.error("❌ Test error:", error);
      Alert.alert("Test greška", error.message);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2563eb" />
        <Text style={styles.loadingText}>Učitavanje podešavanja...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Status servisa</Text>
        <View style={styles.serviceStatusCard}>
          <View style={styles.serviceStatusHeader}>
            <Text style={styles.serviceStatusTitle}>
              Detekcija poziva i SMS
            </Text>
            <Switch
              value={settings.serviceEnabled}
              onValueChange={toggleService}
              trackColor={{ false: "#d1d5db", true: "#2563eb" }}
              thumbColor={settings.serviceEnabled ? "#ffffff" : "#f4f3f4"}
            />
          </View>
          <Text style={styles.serviceStatusDescription}>
            {settings.serviceEnabled
              ? "✅ Servis je aktivan - detektuje pozive i šalje SMS za propuštene pozive"
              : "❌ Servis je neaktivan - pozivi neće biti detektovani"}
          </Text>
          {settings.serviceEnabled && (
            <View style={styles.backgroundInfo}>
              <Text style={styles.backgroundInfoTitle}>� Kako radi:</Text>
              <Text style={styles.backgroundInfoText}>
                • Koristi react-native-call-detection za detekciju poziva{"\n"}•
                Automatski detektuje broj pozivajućeg{"\n"}• Šalje SMS pomoću
                react-native-send-direct-sms{"\n"}• Radi u pozadini dok je
                aplikacija aktivna{"\n"}• SMS se šalje za propuštene pozive
                prema podešavanjima
              </Text>
            </View>
          )}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Upravljanje pozivima</Text>
        <View style={styles.settingCard}>
          <View style={styles.settingHeader}>
            <Text style={styles.settingTitle}>Omogući detekciju poziva</Text>
            <Switch
              value={settings.callHandlingEnabled}
              onValueChange={toggleCallHandling}
              trackColor={{ false: "#d1d5db", true: "#2563eb" }}
              thumbColor={settings.callHandlingEnabled ? "#ffffff" : "#f4f3f4"}
            />
          </View>
          <Text style={styles.settingDescription}>
            Automatski detektuj dolazne pozive i upravljaj SMS odgovorima
          </Text>
        </View>

        {settings.callHandlingEnabled && (
          <View style={styles.modesContainer}>
            <Text style={styles.modesTitle}>
              Način upravljanja SMS porukama:
            </Text>
            {callHandlingModes.map((mode) => (
              <TouchableOpacity
                key={mode.key}
                style={[
                  styles.modeCard,
                  settings.callHandlingMode === mode.key &&
                    styles.selectedModeCard,
                ]}
                onPress={() => selectCallHandlingMode(mode.key)}
              >
                <View style={styles.modeHeader}>
                  <Text
                    style={[
                      styles.modeTitle,
                      settings.callHandlingMode === mode.key &&
                        styles.selectedModeTitle,
                    ]}
                  >
                    {mode.title}
                  </Text>
                  <View
                    style={[
                      styles.radioButton,
                      settings.callHandlingMode === mode.key &&
                        styles.selectedRadioButton,
                    ]}
                  >
                    {settings.callHandlingMode === mode.key && (
                      <View style={styles.radioButtonInner} />
                    )}
                  </View>
                </View>
                <Text
                  style={[
                    styles.modeDescription,
                    settings.callHandlingMode === mode.key &&
                      styles.selectedModeDescription,
                  ]}
                >
                  {mode.description}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>SMS poruke</Text>
        <View style={styles.settingCard}>
          <View style={styles.settingHeader}>
            <Text style={styles.settingTitle}>Omogući SMS odgovore</Text>
            <Switch
              value={settings.smsEnabled}
              onValueChange={toggleSMS}
              trackColor={{ false: "#d1d5db", true: "#2563eb" }}
              thumbColor={settings.smsEnabled ? "#ffffff" : "#f4f3f4"}
            />
          </View>
          <Text style={styles.settingDescription}>
            Automatski pošalji SMS sa linkom za zakazivanje za propuštene pozive
          </Text>
        </View>

        {settings.smsEnabled && (
          <View style={styles.smsTemplateCard}>
            <Text style={styles.smsTemplateTitle}>SMS template</Text>
            <Text style={styles.smsTemplatePreview} numberOfLines={3}>
              {settings.smsTemplate}
            </Text>
            <View style={styles.smsTemplateActions}>
              <TouchableOpacity
                style={styles.editTemplateButton}
                onPress={openSmsEditor}
              >
                <Text style={styles.editTemplateButtonText}>
                  Uredi template
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.previewButton}
                onPress={previewSmsMessage}
              >
                <Text style={styles.previewButtonText}>Pregled</Text>
              </TouchableOpacity>
            </View>
            <TouchableOpacity
              style={styles.testSmsButton}
              onPress={testSmsFunction}
              disabled={isSaving}
            >
              <Text style={styles.testSmsButtonText}>
                {isSaving ? "Šalje..." : "🧪 Test SMS"}
              </Text>
            </TouchableOpacity>
            <Text style={styles.bookingLinkInfo}>
              Vaš booking link: {generateBookingLink()}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Dozvole</Text>
        <View style={styles.permissionsContainer}>
          <View style={styles.permissionItem}>
            <Text style={styles.permissionTitle}>Čitanje stanja telefona</Text>
            <Text
              style={[
                styles.permissionStatus,
                settings.permissionsGranted.phone
                  ? styles.permissionGranted
                  : styles.permissionDenied,
              ]}
            >
              {settings.permissionsGranted.phone ? "Odobreno" : "Potrebno"}
            </Text>
          </View>
          <View style={styles.permissionItem}>
            <Text style={styles.permissionTitle}>Slanje SMS poruka</Text>
            <Text
              style={[
                styles.permissionStatus,
                settings.permissionsGranted.sms
                  ? styles.permissionGranted
                  : styles.permissionDenied,
              ]}
            >
              {settings.permissionsGranted.sms ? "Odobreno" : "Potrebno"}
            </Text>
          </View>
          <View style={styles.permissionItem}>
            <Text style={styles.permissionTitle}>Pristup istoriji poziva</Text>
            <Text
              style={[
                styles.permissionStatus,
                settings.permissionsGranted.callLog
                  ? styles.permissionGranted
                  : styles.permissionDenied,
              ]}
            >
              {settings.permissionsGranted.callLog ? "Odobreno" : "Potrebno"}
            </Text>
          </View>
          <View style={styles.permissionItem}>
            <Text style={styles.permissionTitle}>Čitanje brojeva telefona</Text>
            <Text
              style={[
                styles.permissionStatus,
                settings.permissionsGranted.phoneNumbers
                  ? styles.permissionGranted
                  : styles.permissionDenied,
              ]}
            >
              {settings.permissionsGranted.phoneNumbers
                ? "Odobreno"
                : "Potrebno"}
            </Text>
          </View>
          <View style={styles.permissionItem}>
            <Text style={styles.permissionTitle}>Upravljanje pozivima</Text>
            <Text
              style={[
                styles.permissionStatus,
                settings.permissionsGranted.answerCalls
                  ? styles.permissionGranted
                  : styles.permissionDenied,
              ]}
            >
              {settings.permissionsGranted.answerCalls
                ? "Odobreno"
                : "Potrebno"}
            </Text>
          </View>
        </View>

        <TouchableOpacity
          style={styles.permissionsButton}
          onPress={requestPermissions}
        >
          <Text style={styles.permissionsButtonText}>Zatraži dozvole</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.testButton}
          onPress={simulatePermissions}
        >
          <Text style={styles.testButtonText}>🧪 Test dozvole (Dev)</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.testButton} onPress={testCallDetection}>
          <Text style={styles.testButtonText}>🧪 Test detekcije poziva</Text>
        </TouchableOpacity>
      </View>

      {/* SMS Template Editor Modal */}
      <Modal
        visible={showSmsEditor}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSmsEditor(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Uredi SMS template</Text>

            <TextInput
              style={styles.smsTextInput}
              value={tempSmsTemplate}
              onChangeText={setTempSmsTemplate}
              multiline={true}
              numberOfLines={6}
              placeholder="Unesite SMS poruku..."
              textAlignVertical="top"
            />

            <View style={styles.placeholdersContainer}>
              <Text style={styles.placeholdersTitle}>
                Dostupni placeholders:
              </Text>
              <View style={styles.placeholdersRow}>
                <TouchableOpacity
                  style={styles.placeholderButton}
                  onPress={() => insertPlaceholder("{businessName}")}
                >
                  <Text style={styles.placeholderButtonText}>
                    {"{businessName}"}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.placeholderButton}
                  onPress={() => insertPlaceholder("{bookingLink}")}
                >
                  <Text style={styles.placeholderButtonText}>
                    {"{bookingLink}"}
                  </Text>
                </TouchableOpacity>
              </View>
              <Text style={styles.bookingLinkInfo}>
                Vaš booking link: {generateBookingLink()}
              </Text>
            </View>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.modalSecondaryButton}
                onPress={resetSmsTemplate}
              >
                <Text style={styles.modalSecondaryButtonText}>Reset</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.modalSecondaryButton}
                onPress={previewSmsMessage}
              >
                <Text style={styles.modalSecondaryButtonText}>Pregled</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => setShowSmsEditor(false)}
              >
                <Text style={styles.modalCancelText}>Otkaži</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.modalConfirmButton}
                onPress={saveSmsTemplate}
              >
                <Text style={styles.modalConfirmText}>Sačuvaj</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
    paddingTop: Platform.OS === "android" ? StatusBar.currentHeight : 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f8f9fa",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#6b7280",
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 16,
  },
  serviceStatusCard: {
    backgroundColor: "#ffffff",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  serviceStatusHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  serviceStatusTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1f2937",
    flex: 1,
  },
  serviceStatusDescription: {
    fontSize: 14,
    color: "#6b7280",
    marginBottom: 8,
  },
  backgroundInfo: {
    marginTop: 12,
    padding: 12,
    backgroundColor: "#f0f9ff",
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: "#0ea5e9",
  },
  backgroundInfoTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#0c4a6e",
    marginBottom: 6,
  },
  backgroundInfoText: {
    fontSize: 12,
    color: "#0369a1",
    lineHeight: 18,
  },
  settingCard: {
    backgroundColor: "#ffffff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  settingHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1f2937",
    flex: 1,
  },
  settingDescription: {
    fontSize: 14,
    color: "#6b7280",
  },
  modesContainer: {
    marginTop: 12,
  },
  modesTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 12,
  },
  modeCard: {
    backgroundColor: "#ffffff",
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
    borderWidth: 2,
    borderColor: "#e5e7eb",
  },
  selectedModeCard: {
    borderColor: "#2563eb",
    backgroundColor: "#eff6ff",
  },
  modeHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  modeTitle: {
    fontSize: 15,
    fontWeight: "600",
    color: "#1f2937",
    flex: 1,
  },
  selectedModeTitle: {
    color: "#2563eb",
  },
  modeDescription: {
    fontSize: 13,
    color: "#6b7280",
    lineHeight: 18,
  },
  selectedModeDescription: {
    color: "#1d4ed8",
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: "#d1d5db",
    alignItems: "center",
    justifyContent: "center",
  },
  selectedRadioButton: {
    borderColor: "#2563eb",
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#2563eb",
  },
  permissionsContainer: {
    backgroundColor: "#ffffff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  permissionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
  },
  permissionTitle: {
    fontSize: 16,
    color: "#1f2937",
  },
  permissionStatus: {
    fontSize: 14,
    fontWeight: "600",
  },
  permissionGranted: {
    color: "#10b981",
  },
  permissionDenied: {
    color: "#ef4444",
  },
  permissionsButton: {
    backgroundColor: "#2563eb",
    borderRadius: 8,
    padding: 16,
    alignItems: "center",
  },
  permissionsButtonText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "600",
  },
  testButton: {
    backgroundColor: "#f59e0b",
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  testButtonText: {
    color: "#ffffff",
    fontSize: 14,
    fontWeight: "600",
    textAlign: "center",
  },
  testSmsButton: {
    backgroundColor: "#8b5cf6",
    padding: 10,
    borderRadius: 6,
    marginTop: 8,
  },
  testSmsButtonText: {
    color: "#ffffff",
    fontSize: 13,
    fontWeight: "600",
    textAlign: "center",
  },
  smsTemplateCard: {
    backgroundColor: "#ffffff",
    borderRadius: 12,
    padding: 16,
    marginTop: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  smsTemplateTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 8,
  },
  smsTemplatePreview: {
    fontSize: 14,
    color: "#374151",
    backgroundColor: "#f9fafb",
    padding: 12,
    borderRadius: 6,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "#e5e7eb",
  },
  smsTemplateActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  editTemplateButton: {
    backgroundColor: "#2563eb",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    flex: 1,
    marginRight: 8,
  },
  editTemplateButtonText: {
    color: "#ffffff",
    fontSize: 14,
    fontWeight: "600",
    textAlign: "center",
  },
  previewButton: {
    backgroundColor: "#10b981",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    flex: 1,
    marginLeft: 8,
  },
  previewButtonText: {
    color: "#ffffff",
    fontSize: 14,
    fontWeight: "600",
    textAlign: "center",
  },
  bookingLinkInfo: {
    fontSize: 12,
    color: "#6b7280",
    fontStyle: "italic",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "#ffffff",
    borderRadius: 12,
    padding: 20,
    margin: 20,
    maxHeight: "80%",
    width: "90%",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
    textAlign: "center",
    color: "#1f2937",
  },
  smsTextInput: {
    borderWidth: 1,
    borderColor: "#d1d5db",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
    minHeight: 120,
  },
  placeholdersContainer: {
    marginBottom: 16,
  },
  placeholdersTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#374151",
    marginBottom: 8,
  },
  placeholdersRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 8,
  },
  placeholderButton: {
    backgroundColor: "#f3f4f6",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  placeholderButtonText: {
    fontSize: 12,
    color: "#374151",
    fontFamily: "monospace",
  },
  modalActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  modalSecondaryButton: {
    backgroundColor: "#f3f4f6",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    marginHorizontal: 4,
  },
  modalSecondaryButtonText: {
    color: "#374151",
    fontSize: 14,
    fontWeight: "600",
    textAlign: "center",
  },
  modalButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  modalCancelButton: {
    flex: 1,
    backgroundColor: "#f3f4f6",
    padding: 12,
    borderRadius: 8,
    marginRight: 8,
  },
  modalCancelText: {
    color: "#374151",
    textAlign: "center",
    fontSize: 16,
  },
  modalConfirmButton: {
    flex: 1,
    backgroundColor: "#2563eb",
    padding: 12,
    borderRadius: 8,
    marginLeft: 8,
  },
  modalConfirmText: {
    color: "#ffffff",
    textAlign: "center",
    fontSize: 16,
    fontWeight: "bold",
  },
});

export default MobileSettingsScreen;
