import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  SafeAreaView,
  StatusBar,
  Platform,
} from "react-native";
import { useAuth } from "../context/AuthContext";
import ApiService from "../services/api";

const DashboardScreen = ({ navigation }) => {
  const { user, logout } = useAuth();
  const [dashboardData, setDashboardData] = useState({
    todayBookings: 0,
    weekBookings: 0,
    monthBookings: 0,
    todayBookingsData: [],
  });
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Try to get bookings data
      const response = await ApiService.getBookings();

      // Handle different response formats
      let bookings = [];
      if (Array.isArray(response)) {
        bookings = response;
      } else if (
        response &&
        response.bookings &&
        Array.isArray(response.bookings)
      ) {
        bookings = response.bookings;
      } else if (response && response.data && Array.isArray(response.data)) {
        bookings = response.data;
      } else {
        bookings = [];
      }

      const today = new Date();
      today.setHours(0, 0, 0, 0); // Set to start of day for accurate comparison

      const startOfWeek = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate() - today.getDay()
      );
      startOfWeek.setHours(0, 0, 0, 0);

      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(endOfWeek.getDate() + 6);
      endOfWeek.setHours(23, 59, 59, 999);

      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      startOfMonth.setHours(0, 0, 0, 0);

      const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      endOfMonth.setHours(23, 59, 59, 999);

      const todayBookings = bookings.filter((booking) => {
        if (!booking.date) return false;
        const bookingDate = new Date(booking.date);
        bookingDate.setHours(0, 0, 0, 0);
        return bookingDate.getTime() === today.getTime();
      }).length;

      const weekBookings = bookings.filter((booking) => {
        if (!booking.date) return false;
        const bookingDate = new Date(booking.date);
        return bookingDate >= startOfWeek && bookingDate <= endOfWeek;
      }).length;

      const monthBookings = bookings.filter((booking) => {
        if (!booking.date) return false;
        const bookingDate = new Date(booking.date);
        return bookingDate >= startOfMonth && bookingDate <= endOfMonth;
      }).length;

      // Filtriramo samo današnje termine
      const todayBookingsData = bookings
        .filter((booking) => {
          if (!booking.date) return false;
          const bookingDate = new Date(booking.date);
          bookingDate.setHours(0, 0, 0, 0);
          return bookingDate.getTime() === today.getTime();
        })
        .sort((a, b) => {
          // Sortiramo po vremenu (ranije termine prvo)
          const timeA = a.time || "00:00";
          const timeB = b.time || "00:00";
          return timeA.localeCompare(timeB);
        });

      setDashboardData({
        todayBookings,
        weekBookings,
        monthBookings,
        todayBookingsData,
      });
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      console.error("Error details:", error.message);

      // Set empty data instead of showing error immediately
      setDashboardData({
        todayBookings: 0,
        weekBookings: 0,
        monthBookings: 0,
        recentBookings: [],
      });

      // Only show error if it's not a "no data" situation
      if (error.message && !error.message.includes("404")) {
        Alert.alert("Greška", `Nije moguće učitati podatke: ${error.message}`);
      }
    } finally {
      setIsRefreshing(false);
    }
  };

  const onRefresh = () => {
    setIsRefreshing(true);
    loadDashboardData(true); // Force refresh from API
  };

  const handleLogout = () => {
    Alert.alert("Odjava", "Da li ste sigurni da se želite odjaviti?", [
      { text: "Otkaži", style: "cancel" },
      { text: "Odjavi se", onPress: logout },
    ]);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("sr-RS", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "confirmed":
        return "#10b981";
      case "pending":
        return "#f59e0b";
      case "cancelled":
        return "#ef4444";
      case "completed":
        return "#6b7280";
      default:
        return "#6b7280";
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case "confirmed":
        return "Potvrđeno";
      case "pending":
        return "Na čekanju";
      case "cancelled":
        return "Otkazano";
      case "completed":
        return "Završeno";
      default:
        return status;
    }
  };

  // Helper function to check if appointment is today
  const isToday = (dateString) => {
    const today = new Date();
    const appointmentDate = new Date(dateString);
    return (
      today.getFullYear() === appointmentDate.getFullYear() &&
      today.getMonth() === appointmentDate.getMonth() &&
      today.getDate() === appointmentDate.getDate()
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollContainer}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.welcomeText}>Dobrodošli,</Text>
            <Text style={styles.businessName}>
              {user?.user?.businessName || user?.businessName}
            </Text>
          </View>
          <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
            <Text style={styles.logoutText}>Odjava</Text>
          </TouchableOpacity>
        </View>

        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{dashboardData.todayBookings}</Text>
            <Text style={styles.statLabel}>Danas</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{dashboardData.weekBookings}</Text>
            <Text style={styles.statLabel}>Ova nedelja</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{dashboardData.monthBookings}</Text>
            <Text style={styles.statLabel}>Ovaj mesec</Text>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Brze akcije</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate("Appointments")}
            >
              <Text style={styles.actionButtonText}>📅 Zakazivanja</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate("Services")}
            >
              <Text style={styles.actionButtonText}>💼 Usluge</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate("WorkingHours")}
            >
              <Text style={styles.actionButtonText}>🕒 Radno vreme</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate("MobileSettings")}
            >
              <Text style={styles.actionButtonText}>📱 Mobilne opcije</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Today's Bookings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Današnji termini</Text>
          {dashboardData.todayBookingsData &&
          dashboardData.todayBookingsData.length > 0 ? (
            dashboardData.todayBookingsData.map((booking, index) => (
              <View
                key={booking._id || index}
                style={[
                  styles.appointmentCard,
                  isToday(booking.date) && styles.todayAppointmentCard,
                ]}
              >
                {/* Today indicator */}
                {isToday(booking.date) && (
                  <View style={styles.todayIndicator}>
                    <Text style={styles.todayIndicatorText}>🗓️ DANAS</Text>
                  </View>
                )}

                <View style={styles.appointmentHeader}>
                  <Text style={styles.clientName}>{booking.clientName}</Text>
                  <View
                    style={[
                      styles.statusBadge,
                      { backgroundColor: getStatusColor(booking.status) },
                    ]}
                  >
                    <Text style={styles.statusText}>
                      {getStatusText(booking.status)}
                    </Text>
                  </View>
                </View>

                <Text style={styles.serviceName}>{booking.serviceName}</Text>
                <Text style={styles.appointmentDateTime}>
                  {formatDate(booking.date)} u {formatTime(booking.date)}
                </Text>
                <Text style={styles.clientPhone}>{booking.clientPhone}</Text>
              </View>
            ))
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>Nema termina za danas</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
    paddingTop: Platform.OS === "android" ? StatusBar.currentHeight : 0,
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    backgroundColor: "#ffffff",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
  },
  welcomeText: {
    fontSize: 16,
    color: "#6b7280",
  },
  businessName: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1f2937",
  },
  logoutButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "#ef4444",
    borderRadius: 6,
  },
  logoutText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
    marginTop: 24,
    paddingHorizontal: 20,
  },
  statCard: {
    backgroundColor: "#ffffff",
    padding: 16,
    borderRadius: 12,
    flex: 1,
    marginHorizontal: 4,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#2563eb",
  },
  statLabel: {
    fontSize: 12,
    color: "#6b7280",
    marginTop: 4,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 16,
  },
  quickActions: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  actionButton: {
    backgroundColor: "#ffffff",
    padding: 16,
    borderRadius: 12,
    width: "48%",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#1f2937",
  },
  appointmentCard: {
    backgroundColor: "#ffffff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  todayAppointmentCard: {
    backgroundColor: "#fef3c7",
    borderWidth: 2,
    borderColor: "#f59e0b",
  },
  todayIndicator: {
    backgroundColor: "#f59e0b",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: "flex-start",
    marginBottom: 8,
  },
  todayIndicatorText: {
    color: "#ffffff",
    fontSize: 12,
    fontWeight: "bold",
  },
  appointmentHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  clientName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1f2937",
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: "#ffffff",
    fontSize: 12,
    fontWeight: "600",
  },
  serviceName: {
    fontSize: 14,
    color: "#6b7280",
    marginBottom: 4,
  },
  appointmentDateTime: {
    fontSize: 14,
    color: "#6b7280",
    marginBottom: 4,
  },
  clientPhone: {
    fontSize: 14,
    color: "#6b7280",
  },
  emptyState: {
    backgroundColor: "#ffffff",
    padding: 40,
    borderRadius: 12,
    alignItems: "center",
  },
  emptyStateText: {
    fontSize: 16,
    color: "#6b7280",
  },
});

export default DashboardScreen;
