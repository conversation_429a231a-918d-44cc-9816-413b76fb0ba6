# Call Detection & SMS Functionality

## Pregled

Aplikacija koristi React Native biblioteke za detekciju poziva i slanje SMS poruka umesto Android native koda.

## Korišćene biblioteke

### react-native-call-detection (v1.9.0)
- Detektuje dolazne pozive u realnom vremenu
- Čita broj telefona pozivajućeg
- Prati stanja poziva: Incoming, Offhook, Disconnected, Missed

### react-native-send-direct-sms (v0.2.0)
- Direktno šalje SMS poruke bez otvaranja SMS aplikacije
- Podržava slanje na bilo koji broj telefona

## Potrebne dozvole

Aplikacija zahteva sledeće Android dozvole:

### Osnovne dozvole (obavezne)
- `READ_PHONE_STATE` - čitanje stanja telefona
- `SEND_SMS` - slanje SMS poruka
- `READ_CALL_LOG` - pristup istoriji poziva

### Dodatne dozvole (opcione)
- `READ_PHONE_NUMBERS` - čitanje brojeva telefona
- `ANSWER_PHONE_CALLS` - upravljanje pozivima
- `CALL_PHONE` - pozivanje brojeva
- `FOREGROUND_SERVICE` - rad u pozadini
- `WAKE_LOCK` - sprečavanje spavanja uređaja
- `RECEIVE_BOOT_COMPLETED` - pokretanje nakon restarta
- `SYSTEM_ALERT_WINDOW` - prikaz preko drugih aplikacija

## Kako funkcioniše

### 1. Pokretanje servisa
```javascript
await CallInterceptorService.startService();
```

### 2. Detekcija poziva
- Servis automatski detektuje dolazne pozive
- Čita broj telefona pozivajućeg
- Prati stanje poziva (incoming, answered, missed, etc.)

### 3. Slanje SMS-a
- Za propuštene pozive automatski šalje SMS
- Koristi konfigurisani template sa placeholders
- Podržava {businessName} i {bookingLink} placeholders

### 4. Konfiguracija
Korisnik može da podesi:
- Način rada (uvek dozvoli, samo radno vreme, uvek SMS)
- SMS template
- Uključivanje/isključivanje funkcionalnosti

## Testiranje

### 1. Zatraži dozvole
- Idi na MobileSettingsScreen
- Klikni "Zatraži dozvole"
- Odobri sve potrebne dozvole

### 2. Pokreni servis
- Uključi "Aktiviraj servis"
- Servis će biti aktivan u pozadini

### 3. Testiraj funkcionalnost
- Pozovi telefon sa drugog broja
- Propusti poziv
- SMS će biti automatski poslat

### 4. Test SMS
- Koristi "🧪 Test SMS" dugme za testiranje
- Unesi broj telefona za test

## Ograničenja

1. **Aplikacija mora biti aktivna** - React Native biblioteke rade samo dok je aplikacija u foreground-u
2. **Android only** - iOS ne podržava ovakvu funkcionalnost
3. **Nema automatskog odbijanja poziva** - biblioteke ne mogu da odbiju poziv, samo detektuju
4. **Zavisnost od dozvola** - sve funkcionalnosti zavise od odobrenih dozvola

## Troubleshooting

### Problem: Pozivi se ne detektuju
- Proveri da li su odobrene dozvole
- Proveri da li je servis pokrenut
- Restartuj aplikaciju

### Problem: SMS se ne šalje
- Proveri SMS dozvole
- Proveri da li je SMS funkcionalnost uključena
- Testiraj sa "Test SMS" dugmetom

### Problem: Dozvole se ne odobravaju
- Idi u Settings > Apps > ZakaziAI > Permissions
- Ručno odobri potrebne dozvole
- Restartuj aplikaciju

## Razvojni saveti

- Koristi console.log za praćenje rada servisa
- Testiraj na stvarnom Android uređaju (ne emulator)
- Proveri da li su biblioteke kompatibilne sa verzijom React Native
- Redovno testiraj funkcionalnost nakon izmena
