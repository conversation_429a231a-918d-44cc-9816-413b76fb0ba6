# SMS Functionality with Expo

## Pregled

Aplikacija koristi Expo SMS biblioteku za slanje SMS poruka. Zbog kompatibilnosti sa React Native 0.79 i Expo SDK 53, zamenili smo native biblioteke sa Expo managed workflow bibliotekama.

## Korišćene biblioteke

### expo-sms

- Otvara sistemsku SMS aplikaciju sa unapred popunjenim tekstom
- Kompatibilno sa Expo managed workflow
- Bezbedno i u skladu sa store politikama
- Ne zahteva specijalne dozvole

### expo-linking

- Omogućava deep linking funkcionalnost
- Koristi se za otvaranje SMS aplikacije

## Potrebne dozvole

Aplikacija zahteva minimalne Android dozvole:

### Osnovne dozvole (opciono)

- `SEND_SMS` - za direktno slanje SMS poruka (opciono)

### Napomena o dozvolama

- Expo SMS koristi sistemsku SMS aplikaciju, tako da ne zahteva specijalne dozvole
- Korisnik može da bira da li želi da odobri SEND_SMS dozvolu
- Bez SEND_SMS dozvole, aplikacija će otvoriti SMS aplikaciju sa unapred popunjenim tekstom
- Sa SEND_SMS dozvolom, aplikacija može direktno da pošalje SMS

## Kako funkcioniše

### 1. Pokretanje servisa

```javascript
await CallInterceptorService.startService();
```

### 2. Manuelno slanje SMS-a

- Korisnik unosi broj telefona
- Aplikacija koristi konfigurisani template
- Otvara se SMS aplikacija sa unapred popunjenim tekstom
- Korisnik potvrđuje slanje

### 3. Template sistem

- Koristi konfigurisani template sa placeholders
- Podržava {businessName} i {bookingLink} placeholders
- Template se automatski popunjava sa podacima korisnika

### 4. Konfiguracija

Korisnik može da podesi:

- SMS template
- Način rada SMS funkcionalnosti
- Uključivanje/isključivanje funkcionalnosti

## Testiranje (PRODUKCIJSKA VERZIJA)

### 1. Zatraži dozvole

- Idi na MobileSettingsScreen
- Klikni "Zatraži dozvole"
- Aplikacija će prikazati poruku o zahtevima za dozvole
- Klikni "Razumem"
- Odobri sve potrebne dozvole kada se pojave Android dijalozi
- Proverava se status dozvola u realnom vremenu

### 2. Pokreni servis

- Uključi "Aktiviraj servis"
- Servis će biti aktivan u pozadini
- Aplikacija će prikazati potvrdu da je servis pokrenut

### 3. Testiraj funkcionalnost

- Pozovi telefon sa drugog broja
- Propusti poziv
- SMS će biti automatski poslat
- Proverava console log za detalje

### 4. Test SMS

- Koristi "🧪 Test SMS" dugme za testiranje
- Unesi broj telefona za test
- SMS će biti poslat direktno

### 5. Uklonjena test dugmad

- Uklonjena su "Test dozvole (Dev)" i "Test detekcije poziva" dugmad
- Aplikacija je spremna za produkciju

## Ograničenja

1. **Nema automatske detekcije poziva** - Expo managed workflow ne podržava automatsku detekciju poziva iz bezbednosnih razloga
2. **Manuelno slanje SMS-a** - Korisnik mora da unese broj telefona i potvrdi slanje
3. **Zavisnost od sistemske SMS aplikacije** - Koristi se sistemska SMS aplikacija
4. **Minimalne dozvole** - Zahteva samo SEND_SMS dozvolu (opciono)

## Troubleshooting

### Problem: Pozivi se ne detektuju

- Proveri da li su odobrene dozvole
- Proveri da li je servis pokrenut
- Restartuj aplikaciju

### Problem: SMS se ne šalje

- Proveri SMS dozvole
- Proveri da li je SMS funkcionalnost uključena
- Testiraj sa "Test SMS" dugmetom

### Problem: Dozvole se ne odobravaju

- Idi u Settings > Apps > ZakaziAI > Permissions
- Ručno odobri potrebne dozvole
- Restartuj aplikaciju

## Razvojni saveti

- Koristi console.log za praćenje rada servisa
- Testiraj na stvarnom Android uređaju (ne emulator)
- Proveri da li su biblioteke kompatibilne sa verzijom React Native
- Redovno testiraj funkcionalnost nakon izmena
