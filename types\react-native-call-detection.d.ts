declare module 'react-native-call-detection' {
  export const permissionDenied: string;

  export interface PermissionMessage {
    title: string;
    message: string;
  }

  export type CallState = 'Incoming' | 'Offhook' | 'Disconnected' | 'Missed';

  export type CallDetectorCallback = (event: CallState, phoneNumber?: string) => void;
  export type PermissionDeniedCallback = (error?: string) => void;

  export default class CallDetectorManager {
    constructor(
      callback: CallDetectorCallback,
      readPhoneNumberAndroid?: boolean,
      permissionDeniedCallback?: PermissionDeniedCallback,
      permissionMessage?: PermissionMessage
    );

    dispose(): void;
  }
}
