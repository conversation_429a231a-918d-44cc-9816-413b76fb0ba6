import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Switch,
  Alert,
  ScrollView,
  TextInput,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import CallInterceptorService from "../services/CallInterceptorService";

const SimpleSMSScreen = () => {
  const [serviceEnabled, setServiceEnabled] = useState(false);
  const [smsTemplate, setSmsTemplate] = useState(
    "Zdravo! Hvala što ste pozvali {businessName}. Možete zakazati termin ovde: {bookingLink}"
  );
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem("simpleSMSSettings");
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        setServiceEnabled(settings.serviceEnabled || false);
        setSmsTemplate(settings.smsTemplate || smsTemplate);
      }
    } catch (error) {
      console.error("Error loading settings:", error);
    }
  };

  const saveSettings = async (newSettings) => {
    try {
      await AsyncStorage.setItem("simpleSMSSettings", JSON.stringify(newSettings));
      await CallInterceptorService.saveSettings(newSettings);
    } catch (error) {
      console.error("Error saving settings:", error);
    }
  };

  const toggleService = async (enabled) => {
    try {
      setIsLoading(true);
      console.log("🔄 Toggling SMS service:", enabled);

      if (enabled) {
        console.log("🚀 Starting SMS service...");
        await CallInterceptorService.startService();
        Alert.alert(
          "SMS servis pokrenut",
          "SMS servis je aktivan - možete slati SMS poruke"
        );
      } else {
        console.log("🛑 Stopping SMS service...");
        await CallInterceptorService.stopService();
        Alert.alert("SMS servis zaustavljen", "SMS servis je zaustavljen");
      }

      const newSettings = {
        serviceEnabled: enabled,
        smsTemplate: smsTemplate,
      };

      setServiceEnabled(enabled);
      await saveSettings(newSettings);
      console.log("✅ Service toggled successfully");
    } catch (error) {
      console.error("❌ Error toggling service:", error);
      Alert.alert("Greška", `Došlo je do greške: ${error.message}`);
      setServiceEnabled(!enabled); // Revert on error
    } finally {
      setIsLoading(false);
    }
  };

  const sendManualSMS = () => {
    Alert.prompt(
      "Pošalji SMS",
      "Unesite broj telefona (format: +381601234567):",
      [
        { text: "Otkaži", style: "cancel" },
        {
          text: "Pošalji",
          onPress: async (phoneNumber) => {
            if (phoneNumber) {
              try {
                setIsLoading(true);
                await CallInterceptorService.sendManualSMS(phoneNumber);
                Alert.alert("Uspeh", "SMS je poslat!");
              } catch (error) {
                Alert.alert("Greška", `Greška pri slanju SMS-a: ${error.message}`);
              } finally {
                setIsLoading(false);
              }
            }
          },
        },
      ],
      "plain-text",
      "+381"
    );
  };

  const updateTemplate = async () => {
    try {
      const newSettings = {
        serviceEnabled: serviceEnabled,
        smsTemplate: smsTemplate,
      };
      await saveSettings(newSettings);
      Alert.alert("Uspeh", "SMS template je ažuriran");
    } catch (error) {
      Alert.alert("Greška", "Nije moguće sačuvati template");
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>SMS Servis</Text>
        <Text style={styles.subtitle}>Jednostavno slanje SMS poruka</Text>
      </View>

      <View style={styles.section}>
        <View style={styles.serviceCard}>
          <View style={styles.serviceHeader}>
            <Text style={styles.serviceTitle}>SMS Servis</Text>
            <Switch
              value={serviceEnabled}
              onValueChange={toggleService}
              disabled={isLoading}
              trackColor={{ false: "#d1d5db", true: "#2563eb" }}
              thumbColor={serviceEnabled ? "#ffffff" : "#f4f3f4"}
            />
          </View>
          <Text style={styles.serviceDescription}>
            {serviceEnabled
              ? "✅ SMS servis je aktivan"
              : "❌ SMS servis je neaktivan"}
          </Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>SMS Template</Text>
        <TextInput
          style={styles.textInput}
          value={smsTemplate}
          onChangeText={setSmsTemplate}
          multiline
          placeholder="Unesite SMS template..."
        />
        <TouchableOpacity style={styles.updateButton} onPress={updateTemplate}>
          <Text style={styles.updateButtonText}>Ažuriraj Template</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <TouchableOpacity
          style={styles.sendButton}
          onPress={sendManualSMS}
          disabled={isLoading || !serviceEnabled}
        >
          <Text style={styles.sendButtonText}>
            {isLoading ? "Šalje..." : "📱 Pošalji SMS"}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.infoSection}>
        <Text style={styles.infoTitle}>ℹ️ Kako radi:</Text>
        <Text style={styles.infoText}>
          • Koristi Expo SMS za slanje poruka{"\n"}
          • Otvara SMS aplikaciju sa unapred popunjenim tekstom{"\n"}
          • Kompatibilno sa Expo managed workflow{"\n"}
          • Bezbedno i u skladu sa store politikama
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  header: {
    padding: 20,
    backgroundColor: "#2563eb",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#ffffff",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: "#e2e8f0",
  },
  section: {
    margin: 16,
  },
  serviceCard: {
    backgroundColor: "#ffffff",
    padding: 16,
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  serviceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  serviceTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1f2937",
  },
  serviceDescription: {
    fontSize: 14,
    color: "#6b7280",
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: "#ffffff",
    borderWidth: 1,
    borderColor: "#d1d5db",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: "top",
  },
  updateButton: {
    backgroundColor: "#10b981",
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  updateButtonText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center",
  },
  sendButton: {
    backgroundColor: "#2563eb",
    padding: 16,
    borderRadius: 8,
  },
  sendButtonText: {
    color: "#ffffff",
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
  },
  infoSection: {
    margin: 16,
    padding: 16,
    backgroundColor: "#f0f9ff",
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: "#2563eb",
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: "#6b7280",
    lineHeight: 20,
  },
});

export default SimpleSMSScreen;
